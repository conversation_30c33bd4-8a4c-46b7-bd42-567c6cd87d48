{"logs": [{"outputFile": "com.legal2025.yamy.app-mergeReleaseResources-67:/values-sr/values-sr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4f4f54f2e8e5114b90f7f6b0790bbfac\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-sr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4783", "endColumns": "129", "endOffsets": "4908"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2f35a7ee031df5c1f7272fd186a185b8\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,119", "endOffsets": "161,281"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2833,2944", "endColumns": "110,119", "endOffsets": "2939,3059"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\39f8b51821a67bbb5ffa84ff59d703db\\transformed\\jetified-play-services-base-18.1.0\\res\\values-sr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,447,569,675,825,948,1056,1154,1299,1402,1558,1681,1826,1964,2028,2089", "endColumns": "101,151,121,105,149,122,107,97,144,102,155,122,144,137,63,60,75", "endOffsets": "294,446,568,674,824,947,1055,1153,1298,1401,1557,1680,1825,1963,2027,2088,2164"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3790,3896,4052,4178,4288,4442,4569,4681,4913,5062,5169,5329,5456,5605,5747,5815,5880", "endColumns": "105,155,125,109,153,126,111,101,148,106,159,126,148,141,67,64,79", "endOffsets": "3891,4047,4173,4283,4437,4564,4676,4778,5057,5164,5324,5451,5600,5742,5810,5875,5955"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cda5eae4219c760778cd3db22bb75c66\\transformed\\core-1.13.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "31,32,33,34,35,36,37,65", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3064,3162,3264,3361,3465,3569,3674,6858", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "3157,3259,3356,3460,3564,3669,3785,6954"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0252fce8a9272bd3faf4987742c89595\\transformed\\appcompat-1.1.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,815,897,988,1080,1175,1269,1370,1463,1558,1663,1754,1845,1930,2035,2141,2244,2350,2459,2566,2736,2833", "endColumns": "106,100,105,85,103,121,83,81,90,91,94,93,100,92,94,104,90,90,84,104,105,102,105,108,106,169,96,85", "endOffsets": "207,308,414,500,604,726,810,892,983,1075,1170,1264,1365,1458,1553,1658,1749,1840,1925,2030,2136,2239,2345,2454,2561,2731,2828,2914"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,815,897,988,1080,1175,1269,1370,1463,1558,1663,1754,1845,1930,2035,2141,2244,2350,2459,2566,2736,6772", "endColumns": "106,100,105,85,103,121,83,81,90,91,94,93,100,92,94,104,90,90,84,104,105,102,105,108,106,169,96,85", "endOffsets": "207,308,414,500,604,726,810,892,983,1075,1170,1264,1365,1458,1553,1658,1749,1840,1925,2030,2136,2239,2345,2454,2561,2731,2828,6853"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9b13dd6987e9417ee90818b32ba85674\\transformed\\preference-1.2.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,263,343,495,664,750", "endColumns": "70,86,79,151,168,85,81", "endOffsets": "171,258,338,490,659,745,827"}, "to": {"startLines": "56,58,62,63,66,67,68", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5960,6142,6540,6620,6959,7128,7214", "endColumns": "70,86,79,151,168,85,81", "endOffsets": "6026,6224,6615,6767,7123,7209,7291"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2d1597bb6fbd137a40c0891803f1e6dc\\transformed\\browser-1.8.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,166,266,379", "endColumns": "110,99,112,97", "endOffsets": "161,261,374,472"}, "to": {"startLines": "57,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "6031,6229,6329,6442", "endColumns": "110,99,112,97", "endOffsets": "6137,6324,6437,6535"}}]}]}