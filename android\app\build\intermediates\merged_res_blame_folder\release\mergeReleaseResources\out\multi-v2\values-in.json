{"logs": [{"outputFile": "com.legal2025.yamy.app-mergeReleaseResources-67:/values-in/values-in.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\305e40512bf13f0ccd5d12e0c8791b8d\\transformed\\jetified-play-services-base-18.1.0\\res\\values-in\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,456,578,680,831,954,1065,1167,1329,1430,1590,1712,1863,2003,2063,2119", "endColumns": "102,159,121,101,150,122,110,101,161,100,159,121,150,139,59,55,74", "endOffsets": "295,455,577,679,830,953,1064,1166,1328,1429,1589,1711,1862,2002,2062,2118,2193"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3766,3873,4037,4163,4269,4424,4551,4666,4904,5070,5175,5339,5465,5620,5764,5828,5888", "endColumns": "106,163,125,105,154,126,114,105,165,104,163,125,154,143,63,59,78", "endOffsets": "3868,4032,4158,4264,4419,4546,4661,4767,5065,5170,5334,5460,5615,5759,5823,5883,5962"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7be1c0bb4075e2006ba23d0329c62fdb\\transformed\\browser-1.8.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,362", "endColumns": "99,97,108,100", "endOffsets": "150,248,357,458"}, "to": {"startLines": "57,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "6036,6223,6321,6430", "endColumns": "99,97,108,100", "endOffsets": "6131,6316,6425,6526"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5813dff2fc04f128ab917c6cfbab969f\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-in\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4772", "endColumns": "131", "endOffsets": "4899"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13f2593a09a7e676e6a4e2c9d70ef7ac\\transformed\\core-1.13.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,446,552,670,785", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "145,247,344,441,547,665,780,881"}, "to": {"startLines": "31,32,33,34,35,36,37,65", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3036,3131,3233,3330,3427,3533,3651,6831", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "3126,3228,3325,3422,3528,3646,3761,6927"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e53585da63edf0e2fa4a6374876313c7\\transformed\\appcompat-1.1.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,324,429,516,620,736,819,898,989,1081,1176,1270,1371,1464,1559,1653,1744,1835,1920,2023,2128,2229,2333,2442,2550,2710,2809", "endColumns": "114,103,104,86,103,115,82,78,90,91,94,93,100,92,94,93,90,90,84,102,104,100,103,108,107,159,98,83", "endOffsets": "215,319,424,511,615,731,814,893,984,1076,1171,1265,1366,1459,1554,1648,1739,1830,1915,2018,2123,2224,2328,2437,2545,2705,2804,2888"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,324,429,516,620,736,819,898,989,1081,1176,1270,1371,1464,1559,1653,1744,1835,1920,2023,2128,2229,2333,2442,2550,2710,6747", "endColumns": "114,103,104,86,103,115,82,78,90,91,94,93,100,92,94,93,90,90,84,102,104,100,103,108,107,159,98,83", "endOffsets": "215,319,424,511,615,731,814,893,984,1076,1171,1265,1366,1459,1554,1648,1739,1830,1915,2018,2123,2224,2328,2437,2545,2705,2804,6826"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\76de88675158c32d011d90413b6fc543\\transformed\\preference-1.2.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,174,261,341,477,646,731", "endColumns": "68,86,79,135,168,84,78", "endOffsets": "169,256,336,472,641,726,805"}, "to": {"startLines": "56,58,62,63,66,67,68", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5967,6136,6531,6611,6932,7101,7186", "endColumns": "68,86,79,135,168,84,78", "endOffsets": "6031,6218,6606,6742,7096,7181,7260"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1214412e3b6f657b0a3026c46d581630\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,117", "endOffsets": "159,277"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2809,2918", "endColumns": "108,117", "endOffsets": "2913,3031"}}]}]}