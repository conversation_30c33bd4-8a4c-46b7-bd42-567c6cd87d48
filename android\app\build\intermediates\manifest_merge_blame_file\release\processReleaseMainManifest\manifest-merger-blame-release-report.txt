1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.legal2025.yamy"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10    <!--
11         Required to query activities that can process text, see:
12         https://developer.android.com/training/package-visibility and
13         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
14
15         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
16    -->
17    <queries>
17-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:39:5-44:15
18        <intent>
18-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:40:9-43:18
19            <action android:name="android.intent.action.PROCESS_TEXT" />
19-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:41:13-72
19-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:41:21-70
20
21            <data android:mimeType="text/plain" />
21-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:42:13-50
21-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:42:19-48
22        </intent>
23        <intent>
23-->[:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-8.3.7\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-12:18
24            <action android:name="android.intent.action.GET_CONTENT" />
24-->[:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-8.3.7\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-72
24-->[:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-8.3.7\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:21-69
25
26            <data android:mimeType="*/*" />
26-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:42:13-50
26-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:42:19-48
27        </intent>
28    </queries>
29
30    <uses-permission android:name="android.permission.INTERNET" />
30-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-67
30-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:22-64
31    <uses-permission android:name="android.permission.WAKE_LOCK" />
31-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-68
31-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:22-65
32    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- Permissions options for the `notification` group -->
32-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:5-79
32-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:22-76
33    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- Required by older versions of Google Play services to create IID tokens -->
33-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:5-77
33-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:22-74
34    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
34-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6961c66792b0baae41a4bb36fd4134d\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:26:5-82
34-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6961c66792b0baae41a4bb36fd4134d\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:26:22-79
35    <uses-permission android:name="android.permission.VIBRATE" />
35-->[:flutter_local_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-17.2.4\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-66
35-->[:flutter_local_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-17.2.4\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:22-63
36    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
36-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\251ae3e1e0bd6d03a4314eb3f1846e6d\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:5-98
36-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\251ae3e1e0bd6d03a4314eb3f1846e6d\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:22-95
37
38    <permission
38-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cda5eae4219c760778cd3db22bb75c66\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
39        android:name="com.legal2025.yamy.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
39-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cda5eae4219c760778cd3db22bb75c66\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
40        android:protectionLevel="signature" />
40-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cda5eae4219c760778cd3db22bb75c66\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
41
42    <uses-permission android:name="com.legal2025.yamy.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cda5eae4219c760778cd3db22bb75c66\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cda5eae4219c760778cd3db22bb75c66\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
43
44    <application
45        android:name="android.app.Application"
45-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:4:9-42
46        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
46-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cda5eae4219c760778cd3db22bb75c66\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
47        android:extractNativeLibs="false"
48        android:icon="@mipmap/ic_launcher"
48-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:5:9-43
49        android:label="sharia_law_app" >
49-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:3:9-39
50        <activity
50-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:6:9-27:20
51            android:name="com.legal2025.yamy.MainActivity"
51-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:7:13-41
52            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
52-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:12:13-163
53            android:exported="true"
53-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:8:13-36
54            android:hardwareAccelerated="true"
54-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:13:13-47
55            android:launchMode="singleTop"
55-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:9:13-43
56            android:taskAffinity=""
56-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:10:13-36
57            android:theme="@style/LaunchTheme"
57-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:11:13-47
58            android:windowSoftInputMode="adjustResize" >
58-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:14:13-55
59
60            <!--
61                 Specifies an Android theme to apply to this Activity as soon as
62                 the Android process has started. This theme is visible to the user
63                 while the Flutter UI initializes. After that, this theme continues
64                 to determine the Window background behind the Flutter UI.
65            -->
66            <meta-data
66-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:19:13-22:17
67                android:name="io.flutter.embedding.android.NormalTheme"
67-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:20:15-70
68                android:resource="@style/NormalTheme" />
68-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:21:15-52
69
70            <intent-filter>
70-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:23:13-26:29
71                <action android:name="android.intent.action.MAIN" />
71-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:24:17-68
71-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:24:25-66
72
73                <category android:name="android.intent.category.LAUNCHER" />
73-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:25:17-76
73-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:25:27-74
74            </intent-filter>
75        </activity>
76        <!--
77             Don't delete the meta-data below.
78             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
79        -->
80        <meta-data
80-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:30:9-32:33
81            android:name="flutterEmbedding"
81-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:31:13-44
82            android:value="2" />
82-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:32:13-30
83
84        <service
84-->[:firebase_auth] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_auth-5.6.2\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-12:19
85            android:name="com.google.firebase.components.ComponentDiscoveryService"
85-->[:firebase_auth] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_auth-5.6.2\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:18-89
86            android:directBootAware="true"
86-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f33407928b1ff45454297c1e1a94abf6\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
87            android:exported="false" >
87-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:68:13-37
88            <meta-data
88-->[:firebase_auth] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_auth-5.6.2\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-11:85
89                android:name="com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar"
89-->[:firebase_auth] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_auth-5.6.2\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:17-124
90                android:value="com.google.firebase.components.ComponentRegistrar" />
90-->[:firebase_auth] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_auth-5.6.2\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:17-82
91            <meta-data
91-->[:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-5.6.11\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-11:85
92                android:name="com.google.firebase.components:io.flutter.plugins.firebase.firestore.FlutterFirebaseFirestoreRegistrar"
92-->[:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-5.6.11\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:17-134
93                android:value="com.google.firebase.components.ComponentRegistrar" />
93-->[:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-5.6.11\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:17-82
94            <meta-data
94-->[:firebase_database] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_database-11.3.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-11:85
95                android:name="com.google.firebase.components:io.flutter.plugins.firebase.database.FlutterFirebaseAppRegistrar"
95-->[:firebase_database] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_database-11.3.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:17-127
96                android:value="com.google.firebase.components.ComponentRegistrar" />
96-->[:firebase_database] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_database-11.3.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:17-82
97            <meta-data
97-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:36:13-38:85
98                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
98-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:37:17-128
99                android:value="com.google.firebase.components.ComponentRegistrar" />
99-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:38:17-82
100            <meta-data
100-->[:firebase_storage] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_storage-12.4.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-11:85
101                android:name="com.google.firebase.components:io.flutter.plugins.firebase.storage.FlutterFirebaseAppRegistrar"
101-->[:firebase_storage] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_storage-12.4.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:17-126
102                android:value="com.google.firebase.components.ComponentRegistrar" />
102-->[:firebase_storage] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_storage-12.4.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:17-82
103            <meta-data
103-->[:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-3.15.1\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-11:85
104                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
104-->[:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-3.15.1\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:17-124
105                android:value="com.google.firebase.components.ComponentRegistrar" />
105-->[:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-3.15.1\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:17-82
106            <meta-data
106-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
107                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
107-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
108                android:value="com.google.firebase.components.ComponentRegistrar" />
108-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
109            <meta-data
109-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a292d9a9eb863d426ff7866e66f09617\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:29:13-31:85
110                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
110-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a292d9a9eb863d426ff7866e66f09617\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:30:17-120
111                android:value="com.google.firebase.components.ComponentRegistrar" />
111-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a292d9a9eb863d426ff7866e66f09617\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:31:17-82
112            <meta-data
112-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a292d9a9eb863d426ff7866e66f09617\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:32:13-34:85
113                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
113-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a292d9a9eb863d426ff7866e66f09617\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:33:17-109
114                android:value="com.google.firebase.components.ComponentRegistrar" />
114-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a292d9a9eb863d426ff7866e66f09617\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:34:17-82
115            <meta-data
115-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb879d1880e441b3e772a8c480a3189c\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:17:13-19:85
116                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
116-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb879d1880e441b3e772a8c480a3189c\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:18:17-122
117                android:value="com.google.firebase.components.ComponentRegistrar" />
117-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb879d1880e441b3e772a8c480a3189c\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:19:17-82
118            <meta-data
118-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb879d1880e441b3e772a8c480a3189c\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:20:13-22:85
119                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
119-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb879d1880e441b3e772a8c480a3189c\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:21:17-111
120                android:value="com.google.firebase.components.ComponentRegistrar" />
120-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb879d1880e441b3e772a8c480a3189c\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:22:17-82
121            <meta-data
121-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6961c66792b0baae41a4bb36fd4134d\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:57:13-59:85
122                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
122-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6961c66792b0baae41a4bb36fd4134d\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:58:17-122
123                android:value="com.google.firebase.components.ComponentRegistrar" />
123-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6961c66792b0baae41a4bb36fd4134d\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:59:17-82
124            <meta-data
124-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6961c66792b0baae41a4bb36fd4134d\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:60:13-62:85
125                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
125-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6961c66792b0baae41a4bb36fd4134d\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:61:17-119
126                android:value="com.google.firebase.components.ComponentRegistrar" />
126-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6961c66792b0baae41a4bb36fd4134d\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:62:17-82
127            <meta-data
127-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7af1d9ac59fa8fbd0401542cc57951b\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:30:13-32:85
128                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
128-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7af1d9ac59fa8fbd0401542cc57951b\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:31:17-118
129                android:value="com.google.firebase.components.ComponentRegistrar" />
129-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7af1d9ac59fa8fbd0401542cc57951b\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:32:17-82
130            <meta-data
130-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7af1d9ac59fa8fbd0401542cc57951b\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:33:13-35:85
131                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
131-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7af1d9ac59fa8fbd0401542cc57951b\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:34:17-107
132                android:value="com.google.firebase.components.ComponentRegistrar" />
132-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7af1d9ac59fa8fbd0401542cc57951b\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:35:17-82
133            <meta-data
133-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fef243f8e1cc50669f1fa43b23845a5e\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
134                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
134-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fef243f8e1cc50669f1fa43b23845a5e\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
135                android:value="com.google.firebase.components.ComponentRegistrar" />
135-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fef243f8e1cc50669f1fa43b23845a5e\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
136            <meta-data
136-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fef243f8e1cc50669f1fa43b23845a5e\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
137                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
137-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fef243f8e1cc50669f1fa43b23845a5e\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
138                android:value="com.google.firebase.components.ComponentRegistrar" />
138-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fef243f8e1cc50669f1fa43b23845a5e\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
139            <meta-data
139-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9bd9499f3cc37533f56ddf04cf2650d5\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:25:13-27:85
140                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
140-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9bd9499f3cc37533f56ddf04cf2650d5\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:26:17-120
141                android:value="com.google.firebase.components.ComponentRegistrar" />
141-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9bd9499f3cc37533f56ddf04cf2650d5\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:27:17-82
142            <meta-data
142-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9bd9499f3cc37533f56ddf04cf2650d5\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:28:13-30:85
143                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
143-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9bd9499f3cc37533f56ddf04cf2650d5\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:29:17-117
144                android:value="com.google.firebase.components.ComponentRegistrar" />
144-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9bd9499f3cc37533f56ddf04cf2650d5\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:30:17-82
145            <meta-data
145-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9875c2c7624febad3c0216c266207fc0\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
146                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
146-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9875c2c7624febad3c0216c266207fc0\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
147                android:value="com.google.firebase.components.ComponentRegistrar" />
147-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9875c2c7624febad3c0216c266207fc0\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
148            <meta-data
148-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f33407928b1ff45454297c1e1a94abf6\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
149                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
149-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f33407928b1ff45454297c1e1a94abf6\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
150                android:value="com.google.firebase.components.ComponentRegistrar" />
150-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f33407928b1ff45454297c1e1a94abf6\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
151            <meta-data
151-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9d8e7425fbf42dd6ba0bc12c62816f7\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
152                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
152-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9d8e7425fbf42dd6ba0bc12c62816f7\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
153                android:value="com.google.firebase.components.ComponentRegistrar" />
153-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9d8e7425fbf42dd6ba0bc12c62816f7\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
154        </service>
155        <service
155-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:9-17:72
156            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
156-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:13-107
157            android:exported="false"
157-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:13-37
158            android:permission="android.permission.BIND_JOB_SERVICE" />
158-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:17:13-69
159        <service
159-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:9-24:19
160            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
160-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:13-97
161            android:exported="false" >
161-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-37
162            <intent-filter>
162-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-23:29
163                <action android:name="com.google.firebase.MESSAGING_EVENT" />
163-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:17-78
163-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:25-75
164            </intent-filter>
165        </service>
166
167        <receiver
167-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:26:9-33:20
168            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
168-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:13-98
169            android:exported="true"
169-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:13-36
170            android:permission="com.google.android.c2dm.permission.SEND" >
170-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:13-73
171            <intent-filter>
171-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:13-32:29
172                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
172-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:31:17-81
172-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:31:25-78
173            </intent-filter>
174        </receiver>
175
176        <provider
176-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:41:9-45:38
177            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
177-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:42:13-102
178            android:authorities="com.legal2025.yamy.flutterfirebasemessaginginitprovider"
178-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:43:13-88
179            android:exported="false"
179-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:44:13-37
180            android:initOrder="99" />
180-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:45:13-35
181
182        <activity
182-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
183            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
183-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
184            android:excludeFromRecents="true"
184-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
185            android:exported="true"
185-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
186            android:launchMode="singleTask"
186-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
187            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
187-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
188            <intent-filter>
188-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
189                <action android:name="android.intent.action.VIEW" />
189-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
189-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
190
191                <category android:name="android.intent.category.DEFAULT" />
191-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
191-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
192                <category android:name="android.intent.category.BROWSABLE" />
192-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
192-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
193
194                <data
194-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:42:13-50
195                    android:host="firebase.auth"
196                    android:path="/"
197                    android:scheme="genericidp" />
198            </intent-filter>
199        </activity>
200        <activity
200-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
201            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
201-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
202            android:excludeFromRecents="true"
202-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
203            android:exported="true"
203-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
204            android:launchMode="singleTask"
204-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
205            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
205-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
206            <intent-filter>
206-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
207                <action android:name="android.intent.action.VIEW" />
207-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
207-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
208
209                <category android:name="android.intent.category.DEFAULT" />
209-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
209-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
210                <category android:name="android.intent.category.BROWSABLE" />
210-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
210-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
211
212                <data
212-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:42:13-50
213                    android:host="firebase.auth"
214                    android:path="/"
215                    android:scheme="recaptcha" />
216            </intent-filter>
217        </activity>
218
219        <receiver
219-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6961c66792b0baae41a4bb36fd4134d\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:29:9-40:20
220            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
220-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6961c66792b0baae41a4bb36fd4134d\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:30:13-78
221            android:exported="true"
221-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6961c66792b0baae41a4bb36fd4134d\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:31:13-36
222            android:permission="com.google.android.c2dm.permission.SEND" >
222-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6961c66792b0baae41a4bb36fd4134d\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:32:13-73
223            <intent-filter>
223-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:13-32:29
224                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
224-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:31:17-81
224-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:31:25-78
225            </intent-filter>
226
227            <meta-data
227-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6961c66792b0baae41a4bb36fd4134d\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:37:13-39:40
228                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
228-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6961c66792b0baae41a4bb36fd4134d\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:38:17-92
229                android:value="true" />
229-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6961c66792b0baae41a4bb36fd4134d\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:39:17-37
230        </receiver>
231        <!--
232             FirebaseMessagingService performs security checks at runtime,
233             but set to not exported to explicitly avoid allowing another app to call it.
234        -->
235        <service
235-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6961c66792b0baae41a4bb36fd4134d\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:46:9-53:19
236            android:name="com.google.firebase.messaging.FirebaseMessagingService"
236-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6961c66792b0baae41a4bb36fd4134d\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:47:13-82
237            android:directBootAware="true"
237-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6961c66792b0baae41a4bb36fd4134d\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:48:13-43
238            android:exported="false" >
238-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6961c66792b0baae41a4bb36fd4134d\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:49:13-37
239            <intent-filter android:priority="-500" >
239-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-23:29
240                <action android:name="com.google.firebase.MESSAGING_EVENT" />
240-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:17-78
240-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:25-75
241            </intent-filter>
242        </service>
243
244        <provider
244-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:9-17:20
245            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
245-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-82
246            android:authorities="com.legal2025.yamy.flutter.image_provider"
246-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-74
247            android:exported="false"
247-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-37
248            android:grantUriPermissions="true" >
248-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-47
249            <meta-data
249-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-16:75
250                android:name="android.support.FILE_PROVIDER_PATHS"
250-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-67
251                android:resource="@xml/flutter_image_picker_file_paths" />
251-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:17-72
252        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
253        <service
253-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:9-31:19
254            android:name="com.google.android.gms.metadata.ModuleDependencies"
254-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-78
255            android:enabled="false"
255-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-36
256            android:exported="false" >
256-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-37
257            <intent-filter>
257-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:13-26:29
258                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
258-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:17-94
258-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:25-91
259            </intent-filter>
260
261            <meta-data
261-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:13-30:36
262                android:name="photopicker_activity:0:required"
262-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:17-63
263                android:value="" />
263-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:17-33
264        </service>
265
266        <activity
266-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-11:74
267            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
267-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-74
268            android:exported="false"
268-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-37
269            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
269-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-71
270
271        <service
271-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a78491001511f82c2595d244d8cb59d\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
272            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
272-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a78491001511f82c2595d244d8cb59d\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
273            android:enabled="true"
273-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a78491001511f82c2595d244d8cb59d\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
274            android:exported="false" >
274-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a78491001511f82c2595d244d8cb59d\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
275            <meta-data
275-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a78491001511f82c2595d244d8cb59d\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
276                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
276-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a78491001511f82c2595d244d8cb59d\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
277                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
277-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a78491001511f82c2595d244d8cb59d\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
278        </service>
279
280        <activity
280-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a78491001511f82c2595d244d8cb59d\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
281            android:name="androidx.credentials.playservices.HiddenActivity"
281-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a78491001511f82c2595d244d8cb59d\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
282            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
282-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a78491001511f82c2595d244d8cb59d\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
283            android:enabled="true"
283-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a78491001511f82c2595d244d8cb59d\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
284            android:exported="false"
284-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a78491001511f82c2595d244d8cb59d\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
285            android:fitsSystemWindows="true"
285-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a78491001511f82c2595d244d8cb59d\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
286            android:theme="@style/Theme.Hidden" >
286-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a78491001511f82c2595d244d8cb59d\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
287        </activity>
288        <activity
288-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a904d3e3eacb8c3cce4919673a09b837\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
289            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
289-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a904d3e3eacb8c3cce4919673a09b837\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
290            android:excludeFromRecents="true"
290-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a904d3e3eacb8c3cce4919673a09b837\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
291            android:exported="false"
291-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a904d3e3eacb8c3cce4919673a09b837\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
292            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
292-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a904d3e3eacb8c3cce4919673a09b837\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
293        <!--
294            Service handling Google Sign-In user revocation. For apps that do not integrate with
295            Google Sign-In, this service will never be started.
296        -->
297        <service
297-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a904d3e3eacb8c3cce4919673a09b837\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
298            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
298-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a904d3e3eacb8c3cce4919673a09b837\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
299            android:exported="true"
299-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a904d3e3eacb8c3cce4919673a09b837\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
300            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
300-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a904d3e3eacb8c3cce4919673a09b837\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
301            android:visibleToInstantApps="true" />
301-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a904d3e3eacb8c3cce4919673a09b837\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
302
303        <provider
303-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f33407928b1ff45454297c1e1a94abf6\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
304            android:name="com.google.firebase.provider.FirebaseInitProvider"
304-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f33407928b1ff45454297c1e1a94abf6\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
305            android:authorities="com.legal2025.yamy.firebaseinitprovider"
305-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f33407928b1ff45454297c1e1a94abf6\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
306            android:directBootAware="true"
306-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f33407928b1ff45454297c1e1a94abf6\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
307            android:exported="false"
307-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f33407928b1ff45454297c1e1a94abf6\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
308            android:initOrder="100" />
308-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f33407928b1ff45454297c1e1a94abf6\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
309
310        <activity
310-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f8b51821a67bbb5ffa84ff59d703db\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
311            android:name="com.google.android.gms.common.api.GoogleApiActivity"
311-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f8b51821a67bbb5ffa84ff59d703db\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
312            android:exported="false"
312-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f8b51821a67bbb5ffa84ff59d703db\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
313            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
313-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f8b51821a67bbb5ffa84ff59d703db\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
314
315        <provider
315-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d5f1e97a552c04cf800b7b0ad4bccda\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
316            android:name="androidx.startup.InitializationProvider"
316-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d5f1e97a552c04cf800b7b0ad4bccda\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
317            android:authorities="com.legal2025.yamy.androidx-startup"
317-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d5f1e97a552c04cf800b7b0ad4bccda\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
318            android:exported="false" >
318-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d5f1e97a552c04cf800b7b0ad4bccda\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
319            <meta-data
319-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d5f1e97a552c04cf800b7b0ad4bccda\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
320                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
320-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d5f1e97a552c04cf800b7b0ad4bccda\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
321                android:value="androidx.startup" />
321-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d5f1e97a552c04cf800b7b0ad4bccda\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
322            <meta-data
322-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa917d030eddf7af8909ba2e344f6195\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
323                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
323-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa917d030eddf7af8909ba2e344f6195\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
324                android:value="androidx.startup" />
324-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa917d030eddf7af8909ba2e344f6195\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
325        </provider>
326
327        <uses-library
327-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5246fa6856d03ebbc5d329a5f3859c1\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
328            android:name="androidx.window.extensions"
328-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5246fa6856d03ebbc5d329a5f3859c1\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
329            android:required="false" />
329-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5246fa6856d03ebbc5d329a5f3859c1\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
330        <uses-library
330-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5246fa6856d03ebbc5d329a5f3859c1\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
331            android:name="androidx.window.sidecar"
331-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5246fa6856d03ebbc5d329a5f3859c1\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
332            android:required="false" />
332-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5246fa6856d03ebbc5d329a5f3859c1\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
333
334        <meta-data
334-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f4f54f2e8e5114b90f7f6b0790bbfac\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
335            android:name="com.google.android.gms.version"
335-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f4f54f2e8e5114b90f7f6b0790bbfac\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
336            android:value="@integer/google_play_services_version" />
336-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f4f54f2e8e5114b90f7f6b0790bbfac\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
337
338        <receiver
338-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa917d030eddf7af8909ba2e344f6195\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
339            android:name="androidx.profileinstaller.ProfileInstallReceiver"
339-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa917d030eddf7af8909ba2e344f6195\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
340            android:directBootAware="false"
340-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa917d030eddf7af8909ba2e344f6195\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
341            android:enabled="true"
341-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa917d030eddf7af8909ba2e344f6195\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
342            android:exported="true"
342-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa917d030eddf7af8909ba2e344f6195\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
343            android:permission="android.permission.DUMP" >
343-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa917d030eddf7af8909ba2e344f6195\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
344            <intent-filter>
344-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa917d030eddf7af8909ba2e344f6195\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
345                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
345-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa917d030eddf7af8909ba2e344f6195\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
345-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa917d030eddf7af8909ba2e344f6195\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
346            </intent-filter>
347            <intent-filter>
347-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa917d030eddf7af8909ba2e344f6195\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
348                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
348-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa917d030eddf7af8909ba2e344f6195\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
348-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa917d030eddf7af8909ba2e344f6195\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
349            </intent-filter>
350            <intent-filter>
350-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa917d030eddf7af8909ba2e344f6195\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
351                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
351-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa917d030eddf7af8909ba2e344f6195\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
351-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa917d030eddf7af8909ba2e344f6195\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
352            </intent-filter>
353            <intent-filter>
353-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa917d030eddf7af8909ba2e344f6195\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
354                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
354-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa917d030eddf7af8909ba2e344f6195\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
354-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa917d030eddf7af8909ba2e344f6195\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
355            </intent-filter>
356        </receiver>
357
358        <service
358-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bc67733266f25c9069d64d290f20b8c4\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
359            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
359-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bc67733266f25c9069d64d290f20b8c4\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
360            android:exported="false" >
360-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bc67733266f25c9069d64d290f20b8c4\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
361            <meta-data
361-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bc67733266f25c9069d64d290f20b8c4\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
362                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
362-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bc67733266f25c9069d64d290f20b8c4\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
363                android:value="cct" />
363-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bc67733266f25c9069d64d290f20b8c4\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
364        </service>
365        <service
365-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e72322cfaf94a36292fb7a6522f0e4d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
366            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
366-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e72322cfaf94a36292fb7a6522f0e4d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
367            android:exported="false"
367-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e72322cfaf94a36292fb7a6522f0e4d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
368            android:permission="android.permission.BIND_JOB_SERVICE" >
368-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e72322cfaf94a36292fb7a6522f0e4d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
369        </service>
370
371        <receiver
371-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e72322cfaf94a36292fb7a6522f0e4d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
372            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
372-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e72322cfaf94a36292fb7a6522f0e4d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
373            android:exported="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
373-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e72322cfaf94a36292fb7a6522f0e4d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
374        <activity
374-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db123331b7ad319a2873a64ca6cf5713\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:14:9-18:65
375            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
375-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db123331b7ad319a2873a64ca6cf5713\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:15:13-93
376            android:exported="false"
376-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db123331b7ad319a2873a64ca6cf5713\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:16:13-37
377            android:stateNotNeeded="true"
377-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db123331b7ad319a2873a64ca6cf5713\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:17:13-42
378            android:theme="@style/Theme.PlayCore.Transparent" />
378-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db123331b7ad319a2873a64ca6cf5713\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:18:13-62
379    </application>
380
381</manifest>
