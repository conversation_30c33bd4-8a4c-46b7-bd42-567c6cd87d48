import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/downloaded_pdf.dart';

class DownloadManager {
  static const String _downloadsKey = 'downloaded_pdfs';
  static const String _downloadsFolderName = 'PDFs';

  // الحصول على مجلد التحميلات
  static Future<Directory> _getDownloadsDirectory() async {
    final Directory appDocDir = await getApplicationDocumentsDirectory();
    final Directory downloadsDir = Directory(
      '${appDocDir.path}/$_downloadsFolderName',
    );

    if (!await downloadsDir.exists()) {
      await downloadsDir.create(recursive: true);
    }

    return downloadsDir;
  }

  // حفظ قائمة التحميلات في SharedPreferences
  static Future<void> _saveDownloadsList(List<DownloadedPDF> downloads) async {
    final prefs = await SharedPreferences.getInstance();
    final List<String> downloadsJson =
        downloads.map((pdf) => jsonEncode(pdf.toJson())).toList();
    await prefs.setStringList(_downloadsKey, downloadsJson);
  }

  // تحميل قائمة التحميلات من SharedPreferences
  static Future<List<DownloadedPDF>> getDownloadedFiles() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final List<String>? downloadsJson = prefs.getStringList(_downloadsKey);

      if (downloadsJson == null) return [];

      final List<DownloadedPDF> downloads = [];
      for (final String jsonString in downloadsJson) {
        try {
          final Map<String, dynamic> json = jsonDecode(jsonString);
          final DownloadedPDF pdf = DownloadedPDF.fromJson(json);

          // التحقق من وجود الملف فعلياً
          if (await File(pdf.localPath).exists()) {
            downloads.add(pdf);
          }
        } catch (e) {
          if (kDebugMode) {
            print('خطأ في تحليل ملف PDF: $e');
          }
        }
      }

      return downloads;
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في تحميل قائمة التحميلات: $e');
      }
      return [];
    }
  }

  // تحميل ملف PDF
  static Future<DownloadedPDF?> downloadPDF({
    required String url,
    required String fileName,
    required String subject,
    required String category,
    Function(double)? onProgress,
  }) async {
    try {
      // التحقق من وجود الملف مسبقاً
      final existingFiles = await getDownloadedFiles();
      final existingFile =
          existingFiles
              .where((file) => file.originalUrl == url || file.name == fileName)
              .firstOrNull;

      if (existingFile != null) {
        throw Exception('الملف موجود مسبقاً في التحميلات');
      }

      // إنشاء مجلد التحميلات
      final downloadsDir = await _getDownloadsDirectory();

      // تنظيف اسم الملف
      final cleanFileName = _cleanFileName(fileName);
      final filePath = '${downloadsDir.path}/$cleanFileName';

      // تحميل الملف
      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final file = File(filePath);
        await file.writeAsBytes(response.bodyBytes);

        // إنشاء كائن DownloadedPDF
        final downloadedPDF = DownloadedPDF(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          name: fileName,
          subject: subject,
          category: category,
          localPath: filePath,
          originalUrl: url,
          size: response.bodyBytes.length,
          downloadDate: DateTime.now(),
        );

        // إضافة إلى قائمة التحميلات
        final downloads = await getDownloadedFiles();
        downloads.add(downloadedPDF);
        await _saveDownloadsList(downloads);

        return downloadedPDF;
      } else {
        throw Exception('فشل في تحميل الملف: ${response.statusCode}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في تحميل PDF: $e');
      }
      rethrow;
    }
  }

  // حذف ملف محمل
  static Future<void> deleteFile(DownloadedPDF pdf) async {
    try {
      // حذف الملف من النظام
      final file = File(pdf.localPath);
      if (await file.exists()) {
        await file.delete();
      }

      // إزالة من قائمة التحميلات
      final downloads = await getDownloadedFiles();
      downloads.removeWhere((item) => item.id == pdf.id);
      await _saveDownloadsList(downloads);
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في حذف الملف: $e');
      }
      rethrow;
    }
  }

  // التحقق من وجود ملف محمل
  static Future<bool> isFileDownloaded(String url) async {
    final downloads = await getDownloadedFiles();
    return downloads.any((file) => file.originalUrl == url);
  }

  // الحصول على ملف محمل بواسطة URL
  static Future<DownloadedPDF?> getDownloadedFileByUrl(String url) async {
    final downloads = await getDownloadedFiles();
    try {
      return downloads.firstWhere((file) => file.originalUrl == url);
    } catch (e) {
      return null;
    }
  }

  // تنظيف اسم الملف من الأحرف غير المسموحة
  static String _cleanFileName(String fileName) {
    // إزالة الأحرف غير المسموحة في أسماء الملفات
    String cleaned = fileName.replaceAll(RegExp(r'[<>:"/\\|?*]'), '_');

    // التأكد من وجود امتداد PDF
    if (!cleaned.toLowerCase().endsWith('.pdf')) {
      cleaned += '.pdf';
    }

    return cleaned;
  }

  // الحصول على حجم مجلد التحميلات
  static Future<int> getTotalDownloadsSize() async {
    try {
      final downloads = await getDownloadedFiles();
      return downloads.fold<int>(
        0,
        (int total, DownloadedPDF pdf) => total + pdf.size,
      );
    } catch (e) {
      return 0;
    }
  }

  // مسح جميع التحميلات
  static Future<void> clearAllDownloads() async {
    try {
      final downloads = await getDownloadedFiles();

      // حذف جميع الملفات
      for (final pdf in downloads) {
        final file = File(pdf.localPath);
        if (await file.exists()) {
          await file.delete();
        }
      }

      // مسح قائمة التحميلات
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_downloadsKey);
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في مسح التحميلات: $e');
      }
      rethrow;
    }
  }
}
