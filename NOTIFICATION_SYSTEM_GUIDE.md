# 🔔 دليل نظام الإشعارات الشامل

## 📋 **نظرة عامة**

تم تطوير نظام إشعارات شامل للتطبيق يتضمن:
- إشعارات الهاتف (Push Notifications)
- صفحة الإشعارات داخل التطبيق
- ربط الإشعارات بالفرق الدراسية
- تنظيف تلقائي للإشعارات القديمة

---

## 🚀 **المكونات الرئيسية**

### **1. نموذج البيانات (NotificationModel)**
- دعم أنواع مختلفة من الإشعارات
- معلومات الملف والمادة والفرقة الدراسية
- تنسيق الوقت النسبي
- ألوان وأيقونات مخصصة

### **2. خدمة الإشعارات (NotificationService)**
- إرسال إشعارات للفرق الدراسية المحددة
- تنظيف الإشعارات القديمة (الاحتفاظ بآخر 5)
- دعم Firebase Cloud Messaging
- إشعارات محلية

### **3. صفحة الإشعارات (NotificationsScreen)**
- تصميم عصري متناسق مع التطبيق
- عرض آخر 5 إشعارات
- حالات مختلفة (تحميل، خطأ، فارغ)
- تحديث فوري للإشعارات

### **4. زر الإشعارات في الصفحة الرئيسية**
- عداد الإشعارات غير المقروءة
- تصميم متجاوب
- تحديث فوري

---

## 🔧 **كيفية الاستخدام**

### **إرسال إشعار ملف جديد:**
```dart
await NotificationService.sendNewFileNotification(
  fileName: 'اسم الملف.pdf',
  subjectName: 'اسم المادة',
  academicYear: 'الفرقة الأولى', // أو الثانية، الثالثة، الرابعة
  category: 'أسئلة', // أو امتحانات، ملخصات، الكتاب الرسمي
  fileUrl: 'https://example.com/file.pdf',
);
```

### **الحصول على عدد الإشعارات غير المقروءة:**
```dart
StreamBuilder<int>(
  stream: NotificationService.getUnreadNotificationsCount(),
  builder: (context, snapshot) {
    final count = snapshot.data ?? 0;
    return Text('$count');
  },
)
```

### **فتح صفحة الإشعارات:**
```dart
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => const NotificationsScreen(),
  ),
);
```

---

## 🎯 **الفرق الدراسية المدعومة**

- **الفرقة الأولى** - لون أزرق إلى بنفسجي
- **الفرقة الثانية** - لون وردي إلى برتقالي  
- **الفرقة الثالثة** - لون أخضر إلى سماوي
- **الفرقة الرابعة** - لون أصفر إلى أحمر

---

## 📂 **فئات الملفات المدعومة**

- **أسئلة** - أيقونة ❓
- **امتحانات** - أيقونة 📝
- **ملخصات** - أيقونة 📋
- **الكتاب الرسمي** - أيقونة 📚
- **أشهر المواضع** - أيقونة ⭐

---

## 🔄 **التكامل التلقائي**

النظام مُدمج تلقائياً مع:
- **رفع الملفات**: عند رفع ملف جديد، يتم إرسال إشعار تلقائياً
- **الفرق الدراسية**: الإشعارات تُرسل فقط للمستخدمين في نفس الفرقة
- **التنظيف التلقائي**: حذف الإشعارات القديمة للحفاظ على الأداء

---

## 🧪 **الاختبار**

لاختبار النظام:
```dart
import 'lib/test_notifications.dart';

// تشغيل جميع الاختبارات
await runNotificationTests();

// أو اختبار محدد
await NotificationTester.testNewFileNotification();
```

---

## 📱 **تجربة المستخدم**

### **عند رفع ملف جديد:**
1. الأدمن يرفع ملف PDF جديد
2. النظام يحدد الفرقة الدراسية من المادة
3. إرسال إشعار فوري لجميع طلاب تلك الفرقة
4. ظهور عداد الإشعارات في الصفحة الرئيسية
5. المستخدم يضغط على زر الإشعارات
6. فتح صفحة الإشعارات مع التفاصيل

### **محتوى الإشعار:**
- **العنوان**: "ملف جديد"
- **المحتوى**: "ملف جديد: [اسم الملف] - [اسم المادة]"
- **معلومات إضافية**: فئة الملف، رابط التحميل

---

## 🔧 **الإعدادات المتقدمة**

### **تخصيص عدد الإشعارات:**
يمكن تغيير عدد الإشعارات المحفوظة من 5 إلى أي رقم آخر في:
```dart
// في NotificationService
.limit(5) // غيّر هذا الرقم
```

### **تخصيص ألوان الفرق:**
يمكن تعديل ألوان الفرق في:
```dart
// في NotificationModel
List<int> getAcademicYearColors() {
  // تخصيص الألوان هنا
}
```

---

## 🚨 **ملاحظات مهمة**

1. **الأذونات**: تأكد من منح أذونات الإشعارات
2. **Firebase**: يحتاج إعداد Firebase Cloud Messaging
3. **الأداء**: النظام ينظف الإشعارات القديمة تلقائياً
4. **الأمان**: الإشعارات مرتبطة بالمستخدم والفرقة الدراسية

---

## 📞 **الدعم**

في حالة وجود مشاكل:
1. تحقق من إعدادات Firebase
2. تأكد من أذونات الإشعارات
3. راجع logs التطبيق للأخطاء
4. استخدم ملف الاختبار للتحقق من النظام
