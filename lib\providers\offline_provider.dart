import 'dart:async';
import 'package:flutter/foundation.dart';
import '../services/offline_manager.dart';
import '../services/connectivity_service.dart';
import '../services/sync_service.dart';
import '../models/chat_message.dart';
import '../models/pdf_file.dart';
import '../models/user_data.dart';

/// مزود حالة الوضع غير المتصل
class OfflineProvider extends ChangeNotifier {
  final OfflineManager _offlineManager = OfflineManager();
  final ConnectivityService _connectivity = ConnectivityService();
  final SyncService _syncService = SyncService();

  late StreamSubscription<OfflineStatus> _statusSubscription;
  late StreamSubscription<bool> _connectivitySubscription;

  OfflineStatus _currentStatus = OfflineStatus(
    isOnline: true,
    hasOfflineData: false,
    pendingSyncItems: 0,
    offlineCapabilities: [],
  );

  bool _isInitialized = false;
  bool _isSyncing = false;
  String? _lastSyncError;

  // Getters
  OfflineStatus get currentStatus => _currentStatus;
  bool get isOnline => _currentStatus.isOnline;
  bool get hasOfflineData => _currentStatus.hasOfflineData;
  bool get isInitialized => _isInitialized;
  bool get isSyncing => _isSyncing;
  String? get lastSyncError => _lastSyncError;
  int get pendingSyncItems => _currentStatus.pendingSyncItems;

  /// تهيئة المزود
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // تهيئة الخدمات
      await _offlineManager.initialize();
      
      // مراقبة حالة الوضع غير المتصل
      _statusSubscription = _offlineManager.statusStream.listen((status) {
        _currentStatus = status;
        notifyListeners();
      });

      // مراقبة حالة الاتصال
      _connectivitySubscription = _connectivity.connectionStream.listen((isConnected) {
        if (isConnected && _currentStatus.pendingSyncItems > 0) {
          _performAutoSync();
        }
      });

      // تحديث الحالة الأولية
      _currentStatus = _offlineManager.getCurrentStatus();
      _isInitialized = true;
      
      notifyListeners();
      debugPrint('✅ تم تهيئة مزود الوضع غير المتصل');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة مزود الوضع غير المتصل: $e');
      rethrow;
    }
  }

  /// مزامنة تلقائية
  Future<void> _performAutoSync() async {
    if (_isSyncing) return;

    _isSyncing = true;
    _lastSyncError = null;
    notifyListeners();

    try {
      final result = await _syncService.syncAll();
      if (!result.success) {
        _lastSyncError = result.message;
      }
    } catch (e) {
      _lastSyncError = e.toString();
      debugPrint('❌ خطأ في المزامنة التلقائية: $e');
    } finally {
      _isSyncing = false;
      notifyListeners();
    }
  }

  // ==================== إدارة الرسائل ====================

  /// إرسال رسالة (متصل أو غير متصل)
  Future<ChatMessage> sendMessage({
    required String content,
    required String senderId,
    required String senderName,
    required String roomId,
    String? replyToId,
    String? replyToContent,
    String? imageUrl,
    String? fileUrl,
    String? fileName,
    MessageType type = MessageType.text,
  }) async {
    try {
      final message = await _offlineManager.sendOfflineMessage(
        content: content,
        senderId: senderId,
        senderName: senderName,
        roomId: roomId,
        replyToId: replyToId,
        replyToContent: replyToContent,
        imageUrl: imageUrl,
        fileUrl: fileUrl,
        fileName: fileName,
        type: type,
      );

      notifyListeners();
      return message;
    } catch (e) {
      debugPrint('❌ خطأ في إرسال الرسالة: $e');
      rethrow;
    }
  }

  /// الحصول على رسائل غرفة
  List<ChatMessage> getMessages(String roomId, {int limit = 50}) {
    return _offlineManager.getOfflineMessages(roomId, limit: limit);
  }

  /// البحث في الرسائل
  List<ChatMessage> searchMessages(String query, {String? roomId}) {
    return _offlineManager.searchOfflineMessages(query, roomId: roomId);
  }

  // ==================== إدارة ملفات PDF ====================

  /// تحميل PDF للوصول غير المتصل
  Future<bool> downloadPdfForOffline(PdfFile pdfFile) async {
    try {
      final success = await _offlineManager.downloadPdfForOffline(pdfFile);
      if (success) {
        notifyListeners();
      }
      return success;
    } catch (e) {
      debugPrint('❌ خطأ في تحميل PDF: $e');
      return false;
    }
  }

  /// الحصول على ملفات PDF المتاحة غير متصلة
  List<PdfFile> getOfflinePdfs({String? subject, String? category}) {
    return _offlineManager.getOfflinePdfs(subject: subject, category: category);
  }

  /// فتح ملف PDF غير متصل
  Future<String?> openOfflinePdf(String pdfId) async {
    return await _offlineManager.openOfflinePdf(pdfId);
  }

  /// حذف PDF من التخزين المحلي
  Future<bool> removeOfflinePdf(String pdfId) async {
    try {
      final success = await _offlineManager.removeOfflinePdf(pdfId);
      if (success) {
        notifyListeners();
      }
      return success;
    } catch (e) {
      debugPrint('❌ خطأ في حذف PDF: $e');
      return false;
    }
  }

  // ==================== إدارة بيانات المستخدم ====================

  /// الحصول على بيانات المستخدم
  UserData? getUserData() {
    return _offlineManager.getOfflineUserData();
  }

  /// تحديث بيانات المستخدم
  Future<void> updateUserData(UserData userData) async {
    try {
      await _offlineManager.updateOfflineUserData(userData);
      notifyListeners();
    } catch (e) {
      debugPrint('❌ خطأ في تحديث بيانات المستخدم: $e');
      rethrow;
    }
  }

  // ==================== إدارة المزامنة ====================

  /// فرض المزامنة الفورية
  Future<SyncResult> forceSync() async {
    if (_isSyncing) {
      return SyncResult(
        success: false,
        message: 'المزامنة قيد التنفيذ بالفعل',
      );
    }

    _isSyncing = true;
    _lastSyncError = null;
    notifyListeners();

    try {
      final result = await _offlineManager.forceSync();
      if (!result.success) {
        _lastSyncError = result.message;
      }
      return result;
    } catch (e) {
      _lastSyncError = e.toString();
      debugPrint('❌ خطأ في فرض المزامنة: $e');
      return SyncResult(
        success: false,
        message: 'فشلت المزامنة: $e',
      );
    } finally {
      _isSyncing = false;
      notifyListeners();
    }
  }

  /// الحصول على حالة المزامنة
  SyncStatus getSyncStatus() {
    return _syncService.getSyncStatus();
  }

  // ==================== إدارة التخزين ====================

  /// الحصول على إحصائيات التخزين
  Map<String, dynamic> getStorageStatistics() {
    return _offlineManager.getStorageStatistics();
  }

  /// تنظيف البيانات القديمة
  Future<void> cleanupOldData() async {
    try {
      await _offlineManager.cleanupOfflineData();
      notifyListeners();
    } catch (e) {
      debugPrint('❌ خطأ في تنظيف البيانات: $e');
      rethrow;
    }
  }

  // ==================== الوظائف المساعدة ====================

  /// التحقق من توفر قدرة معينة
  bool hasCapability(OfflineCapability capability) {
    return _currentStatus.hasCapability(capability);
  }

  /// التحقق من إمكانية إرسال الرسائل
  bool canSendMessages() {
    return hasCapability(OfflineCapability.writeMessages);
  }

  /// التحقق من إمكانية عرض ملفات PDF
  bool canViewPdfs() {
    return hasCapability(OfflineCapability.viewPdfs);
  }

  /// التحقق من إمكانية البحث
  bool canSearch() {
    return hasCapability(OfflineCapability.searchContent);
  }

  /// الحصول على رسالة حالة مناسبة للمستخدم
  String getStatusMessage() {
    if (!_isInitialized) return 'جاري التهيئة...';
    if (_isSyncing) return 'جاري المزامنة...';
    if (_lastSyncError != null) return 'خطأ في المزامنة: $_lastSyncError';
    return _currentStatus.statusDescription;
  }

  /// التحقق من الحاجة لعرض تحذير عدم الاتصال
  bool shouldShowOfflineWarning() {
    return !isOnline && !hasOfflineData;
  }

  /// التحقق من الحاجة لعرض مؤشر المزامنة
  bool shouldShowSyncIndicator() {
    return isOnline && (pendingSyncItems > 0 || _isSyncing);
  }

  /// الحصول على لون مؤشر الحالة
  String getStatusColor() {
    if (!isOnline && !hasOfflineData) return 'red';
    if (!isOnline && hasOfflineData) return 'orange';
    if (isOnline && pendingSyncItems > 0) return 'blue';
    return 'green';
  }

  /// إعادة تحميل البيانات
  Future<void> refresh() async {
    try {
      if (isOnline) {
        await _performAutoSync();
      }
      _currentStatus = _offlineManager.getCurrentStatus();
      notifyListeners();
    } catch (e) {
      debugPrint('❌ خطأ في إعادة التحميل: $e');
    }
  }

  @override
  void dispose() {
    _statusSubscription.cancel();
    _connectivitySubscription.cancel();
    _offlineManager.dispose();
    super.dispose();
  }
}

/// نتيجة عملية في الوضع غير المتصل
class OfflineOperationResult {
  final bool success;
  final String message;
  final dynamic data;

  OfflineOperationResult({
    required this.success,
    required this.message,
    this.data,
  });

  factory OfflineOperationResult.success({String? message, dynamic data}) {
    return OfflineOperationResult(
      success: true,
      message: message ?? 'تمت العملية بنجاح',
      data: data,
    );
  }

  factory OfflineOperationResult.failure(String message) {
    return OfflineOperationResult(
      success: false,
      message: message,
    );
  }
}
