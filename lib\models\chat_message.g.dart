// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chat_message.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ChatMessageAdapter extends TypeAdapter<ChatMessage> {
  @override
  final int typeId = 0;

  @override
  ChatMessage read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ChatMessage(
      id: fields[0] as String,
      content: fields[1] as String,
      senderId: fields[2] as String,
      senderName: fields[3] as String,
      timestamp: fields[4] as DateTime,
      roomId: fields[5] as String,
      isSent: fields[6] as bool,
      isRead: fields[7] as bool,
      replyToId: fields[8] as String?,
      replyToContent: fields[9] as String?,
      imageUrl: fields[10] as String?,
      fileUrl: fields[11] as String?,
      fileName: fields[12] as String?,
      type: fields[13] as MessageType,
      isEdited: fields[14] as bool,
      editedAt: fields[15] as DateTime?,
      reactions: (fields[16] as List?)?.cast<String>() ?? [],
      isDeleted: fields[17] as bool,
      localFilePath: fields[18] as String?,
      needsSync: fields[19] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, ChatMessage obj) {
    writer
      ..writeByte(20)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.content)
      ..writeByte(2)
      ..write(obj.senderId)
      ..writeByte(3)
      ..write(obj.senderName)
      ..writeByte(4)
      ..write(obj.timestamp)
      ..writeByte(5)
      ..write(obj.roomId)
      ..writeByte(6)
      ..write(obj.isSent)
      ..writeByte(7)
      ..write(obj.isRead)
      ..writeByte(8)
      ..write(obj.replyToId)
      ..writeByte(9)
      ..write(obj.replyToContent)
      ..writeByte(10)
      ..write(obj.imageUrl)
      ..writeByte(11)
      ..write(obj.fileUrl)
      ..writeByte(12)
      ..write(obj.fileName)
      ..writeByte(13)
      ..write(obj.type)
      ..writeByte(14)
      ..write(obj.isEdited)
      ..writeByte(15)
      ..write(obj.editedAt)
      ..writeByte(16)
      ..write(obj.reactions)
      ..writeByte(17)
      ..write(obj.isDeleted)
      ..writeByte(18)
      ..write(obj.localFilePath)
      ..writeByte(19)
      ..write(obj.needsSync);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ChatMessageAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class MessageTypeAdapter extends TypeAdapter<MessageType> {
  @override
  final int typeId = 1;

  @override
  MessageType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return MessageType.text;
      case 1:
        return MessageType.image;
      case 2:
        return MessageType.file;
      case 3:
        return MessageType.voice;
      case 4:
        return MessageType.video;
      case 5:
        return MessageType.location;
      case 6:
        return MessageType.system;
      default:
        return MessageType.text;
    }
  }

  @override
  void write(BinaryWriter writer, MessageType obj) {
    switch (obj) {
      case MessageType.text:
        writer.writeByte(0);
        break;
      case MessageType.image:
        writer.writeByte(1);
        break;
      case MessageType.file:
        writer.writeByte(2);
        break;
      case MessageType.voice:
        writer.writeByte(3);
        break;
      case MessageType.video:
        writer.writeByte(4);
        break;
      case MessageType.location:
        writer.writeByte(5);
        break;
      case MessageType.system:
        writer.writeByte(6);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MessageTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
