import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

/// Widget لعرض حالة التحميل التدريجي للرسائل
class PaginationLoadingWidget extends StatefulWidget {
  final bool isLoading;
  final bool hasMore;
  final VoidCallback? onLoadMore;
  final String loadingText;
  final String noMoreText;

  const PaginationLoadingWidget({
    super.key,
    required this.isLoading,
    required this.hasMore,
    this.onLoadMore,
    this.loadingText = 'جاري تحميل المزيد...',
    this.noMoreText = 'لا توجد رسائل أقدم',
  });

  @override
  State<PaginationLoadingWidget> createState() =>
      _PaginationLoadingWidgetState();
}

class _PaginationLoadingWidgetState extends State<PaginationLoadingWidget>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _fadeController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _fadeController, curve: Curves.easeIn));

    if (widget.isLoading) {
      _pulseController.repeat(reverse: true);
    }

    _fadeController.forward();
  }

  @override
  void didUpdateWidget(PaginationLoadingWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.isLoading != oldWidget.isLoading) {
      if (widget.isLoading) {
        _pulseController.repeat(reverse: true);
      } else {
        _pulseController.stop();
      }
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isLoading && !widget.hasMore) {
      return _buildNoMoreWidget();
    }

    if (widget.isLoading) {
      return _buildLoadingWidget();
    }

    if (widget.hasMore) {
      return _buildLoadMoreButton();
    }

    return const SizedBox.shrink();
  }

  Widget _buildLoadingWidget() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AnimatedBuilder(
              animation: _pulseAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _pulseAnimation.value,
                  child: Container(
                    width: 20,
                    height: 20,
                    decoration: BoxDecoration(
                      color: const Color(0xFF6366F1).withValues(alpha: 0.7),
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                );
              },
            ),
            const SizedBox(width: 12),

            Text(
              widget.loadingText,
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),

            const SizedBox(width: 12),

            SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(
                  const Color(0xFF6366F1).withValues(alpha: 0.7),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadMoreButton() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 20),
        child: Center(
          child: GestureDetector(
            onTap: widget.onLoadMore,
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    const Color(0xFF6366F1).withValues(alpha: 0.1),
                    const Color(0xFF8B5CF6).withValues(alpha: 0.1),
                  ],
                ),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: const Color(0xFF6366F1).withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.keyboard_arrow_up_rounded,
                    color: const Color(0xFF6366F1),
                    size: 20,
                  ),
                  const SizedBox(width: 8),

                  Text(
                    'تحميل رسائل أقدم',
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: const Color(0xFF6366F1),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNoMoreWidget() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 4,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[400],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(width: 8),

            Container(
              width: 6,
              height: 6,
              decoration: BoxDecoration(
                color: Colors.grey[400],
                borderRadius: BorderRadius.circular(3),
              ),
            ),
            const SizedBox(width: 8),

            Container(
              width: 4,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[400],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(width: 16),

            Text(
              widget.noMoreText,
              style: GoogleFonts.cairo(
                fontSize: 12,
                color: Colors.grey[500],
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Widget لعرض مؤشر السحب للتحديث
class PullToRefreshIndicator extends StatefulWidget {
  final bool isRefreshing;
  final double pullDistance;
  final double triggerDistance;

  const PullToRefreshIndicator({
    super.key,
    required this.isRefreshing,
    required this.pullDistance,
    this.triggerDistance = 80.0,
  });

  @override
  State<PullToRefreshIndicator> createState() => _PullToRefreshIndicatorState();
}

class _PullToRefreshIndicatorState extends State<PullToRefreshIndicator>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();

    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _rotationAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _rotationController, curve: Curves.linear),
    );

    if (widget.isRefreshing) {
      _rotationController.repeat();
    }
  }

  @override
  void didUpdateWidget(PullToRefreshIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.isRefreshing != oldWidget.isRefreshing) {
      if (widget.isRefreshing) {
        _rotationController.repeat();
      } else {
        _rotationController.stop();
      }
    }
  }

  @override
  void dispose() {
    _rotationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final progress = (widget.pullDistance / widget.triggerDistance).clamp(
      0.0,
      1.0,
    );
    final isTriggered = widget.pullDistance >= widget.triggerDistance;

    return SizedBox(
      height: widget.pullDistance.clamp(0.0, 100.0),
      child: Center(
        child: AnimatedBuilder(
          animation: _rotationAnimation,
          builder: (context, child) {
            return Transform.rotate(
              angle:
                  widget.isRefreshing
                      ? _rotationAnimation.value * 2 * 3.14159
                      : progress * 2 * 3.14159,
              child: Container(
                width: 30,
                height: 30,
                decoration: BoxDecoration(
                  color:
                      isTriggered
                          ? const Color(0xFF6366F1)
                          : const Color(0xFF6366F1).withValues(alpha: progress),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Icon(
                  widget.isRefreshing
                      ? Icons.refresh_rounded
                      : Icons.keyboard_arrow_up_rounded,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
