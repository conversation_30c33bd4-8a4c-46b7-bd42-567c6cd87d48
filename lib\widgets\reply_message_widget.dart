import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../models/enhanced_chat_message.dart';

/// Widget لعرض الرسالة المرجعية داخل الرسالة
class ReplyMessageWidget extends StatelessWidget {
  final EnhancedChatMessage message;
  final bool isFromCurrentUser;
  final bool isDarkMode;
  final VoidCallback? onTap;

  const ReplyMessageWidget({
    super.key,
    required this.message,
    required this.isFromCurrentUser,
    this.isDarkMode = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    if (!message.isReply) return const SizedBox.shrink();

    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: _getReplyBackgroundColor(),
          borderRadius: BorderRadius.circular(8),
          border: Border(
            right: BorderSide(
              color: _getReplyAccentColor(),
              width: 3,
            ),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // اسم المرسل الأصلي
            Text(
              message.replyToSenderName ?? 'مستخدم',
              style: GoogleFonts.cairo(
                fontSize: 11,
                fontWeight: FontWeight.w600,
                color: _getReplyAccentColor(),
              ),
            ),
            
            const SizedBox(height: 2),
            
            // محتوى الرسالة المرجعية
            Row(
              children: [
                // أيقونة نوع الرسالة
                if (message.replyTypeIcon.isNotEmpty) ...[
                  Text(
                    message.replyTypeIcon,
                    style: const TextStyle(fontSize: 12),
                  ),
                  const SizedBox(width: 4),
                ],
                
                // نص الرسالة
                Expanded(
                  child: Text(
                    _getReplyContent(),
                    style: GoogleFonts.cairo(
                      fontSize: 11,
                      color: _getReplyTextColor(),
                      fontStyle: message.replyToMessageType == MessageType.text 
                          ? FontStyle.normal 
                          : FontStyle.italic,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getReplyBackgroundColor() {
    if (isFromCurrentUser) {
      return isDarkMode 
          ? Colors.white.withValues(alpha: 0.1)
          : Colors.white.withValues(alpha: 0.3);
    } else {
      return isDarkMode 
          ? const Color(0xFF334155).withValues(alpha: 0.5)
          : Colors.grey[200]!.withValues(alpha: 0.7);
    }
  }

  Color _getReplyAccentColor() {
    return const Color(0xFF6366F1);
  }

  Color _getReplyTextColor() {
    if (isFromCurrentUser) {
      return isDarkMode 
          ? Colors.white.withValues(alpha: 0.8)
          : Colors.white.withValues(alpha: 0.9);
    } else {
      return isDarkMode 
          ? Colors.white.withValues(alpha: 0.7)
          : Colors.black.withValues(alpha: 0.7);
    }
  }

  String _getReplyContent() {
    if (message.replyToMessageContent == null || message.replyToMessageContent!.isEmpty) {
      return _getTypeBasedContent();
    }
    return message.replyToMessageContent!;
  }

  String _getTypeBasedContent() {
    switch (message.replyToMessageType) {
      case MessageType.image:
        return 'صورة';
      case MessageType.file:
        return 'ملف';
      case MessageType.voice:
        return 'رسالة صوتية';
      case MessageType.video:
        return 'فيديو';
      case MessageType.system:
        return 'رسالة نظام';
      default:
        return 'رسالة';
    }
  }
}

/// Widget لعرض الرسالة مع الرد المدمج
class MessageWithReplyWidget extends StatefulWidget {
  final EnhancedChatMessage message;
  final bool isFromCurrentUser;
  final bool isDarkMode;
  final VoidCallback? onReplyTap;
  final Widget messageContent;

  const MessageWithReplyWidget({
    super.key,
    required this.message,
    required this.isFromCurrentUser,
    required this.messageContent,
    this.isDarkMode = false,
    this.onReplyTap,
  });

  @override
  State<MessageWithReplyWidget> createState() => _MessageWithReplyWidgetState();
}

class _MessageWithReplyWidgetState extends State<MessageWithReplyWidget>
    with SingleTickerProviderStateMixin {
  
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleReplyTap() {
    _animationController.forward().then((_) {
      _animationController.reverse();
    });
    
    widget.onReplyTap?.call();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: widget.isFromCurrentUser 
          ? CrossAxisAlignment.end 
          : CrossAxisAlignment.start,
      children: [
        // الرسالة المرجعية
        if (widget.message.isReply)
          AnimatedBuilder(
            animation: _scaleAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _scaleAnimation.value,
                child: ReplyMessageWidget(
                  message: widget.message,
                  isFromCurrentUser: widget.isFromCurrentUser,
                  isDarkMode: widget.isDarkMode,
                  onTap: _handleReplyTap,
                ),
              );
            },
          ),
        
        // محتوى الرسالة الأساسي
        widget.messageContent,
      ],
    );
  }
}

/// Widget لعرض خط الربط بين الرسالة والرد
class ReplyConnectionWidget extends StatelessWidget {
  final bool isFromCurrentUser;
  final bool isDarkMode;
  final double height;

  const ReplyConnectionWidget({
    super.key,
    required this.isFromCurrentUser,
    this.isDarkMode = false,
    this.height = 20,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 2,
      height: height,
      margin: EdgeInsets.only(
        left: isFromCurrentUser ? 0 : 16,
        right: isFromCurrentUser ? 16 : 0,
      ),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            const Color(0xFF6366F1).withValues(alpha: 0.8),
            const Color(0xFF6366F1).withValues(alpha: 0.2),
          ],
        ),
        borderRadius: BorderRadius.circular(1),
      ),
    );
  }
}

/// Widget لعرض مؤشر الرد السريع
class QuickReplyIndicator extends StatefulWidget {
  final bool isVisible;
  final bool isFromCurrentUser;

  const QuickReplyIndicator({
    super.key,
    required this.isVisible,
    required this.isFromCurrentUser,
  });

  @override
  State<QuickReplyIndicator> createState() => _QuickReplyIndicatorState();
}

class _QuickReplyIndicatorState extends State<QuickReplyIndicator>
    with SingleTickerProviderStateMixin {
  
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.elasticOut,
    ));

    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeIn,
    ));
  }

  @override
  void didUpdateWidget(QuickReplyIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isVisible != oldWidget.isVisible) {
      if (widget.isVisible) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Opacity(
          opacity: _opacityAnimation.value,
          child: Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: const Color(0xFF6366F1),
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF6366F1).withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(
                    Icons.reply_rounded,
                    color: Colors.white,
                    size: 16,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'اسحب للرد',
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
