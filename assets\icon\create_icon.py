#!/usr/bin/env python3
"""
إنشاء أيقونة التطبيق Legal2025
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_app_icon():
    """إنشاء أيقونة التطبيق"""
    
    # إعدادات الأيقونة
    size = 1024  # حجم الأيقونة الأساسي
    background_color = "#1976D2"  # أزرق Material Design
    text_color = "#FFFFFF"  # أبيض
    
    # إنشاء الصورة
    img = Image.new('RGBA', (size, size), background_color)
    draw = ImageDraw.Draw(img)
    
    # رسم دائرة خلفية
    margin = 50
    circle_bbox = [margin, margin, size - margin, size - margin]
    draw.ellipse(circle_bbox, fill=background_color, outline="#0D47A1", width=10)
    
    # إضافة رمز العدالة (ميزان)
    # رسم قاعدة الميزان
    base_y = size * 0.8
    base_width = size * 0.6
    base_x = (size - base_width) // 2
    draw.rectangle([base_x, base_y, base_x + base_width, base_y + 20], fill=text_color)
    
    # رسم عمود الميزان
    pole_x = size // 2
    pole_width = 15
    draw.rectangle([pole_x - pole_width//2, size * 0.3, pole_x + pole_width//2, base_y], fill=text_color)
    
    # رسم كفتي الميزان
    scale_y = size * 0.4
    scale_width = size * 0.25
    scale_height = 15
    
    # الكفة اليسرى
    left_scale_x = size * 0.25
    draw.rectangle([left_scale_x, scale_y, left_scale_x + scale_width, scale_y + scale_height], fill=text_color)
    
    # الكفة اليمنى
    right_scale_x = size * 0.75 - scale_width
    draw.rectangle([right_scale_x, scale_y, right_scale_x + scale_width, scale_y + scale_height], fill=text_color)
    
    # رسم خيوط الميزان
    draw.line([pole_x, size * 0.35, left_scale_x + scale_width//2, scale_y], fill=text_color, width=5)
    draw.line([pole_x, size * 0.35, right_scale_x + scale_width//2, scale_y], fill=text_color, width=5)
    
    # إضافة نص "2025"
    try:
        # محاولة استخدام خط عربي إذا كان متوفراً
        font_size = size // 8
        font = ImageFont.truetype("arial.ttf", font_size)
    except:
        # استخدام الخط الافتراضي
        font = ImageFont.load_default()
    
    text = "2025"
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    text_x = (size - text_width) // 2
    text_y = size * 0.15
    
    draw.text((text_x, text_y), text, fill=text_color, font=font)
    
    # حفظ الأيقونة الأساسية
    img.save('app_icon.png', 'PNG')
    
    # إنشاء نسخة للـ foreground (بدون خلفية)
    fg_img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    fg_draw = ImageDraw.Draw(fg_img)
    
    # رسم الميزان فقط للـ foreground
    # رسم قاعدة الميزان
    fg_draw.rectangle([base_x, base_y, base_x + base_width, base_y + 20], fill=text_color)
    
    # رسم عمود الميزان
    fg_draw.rectangle([pole_x - pole_width//2, size * 0.3, pole_x + pole_width//2, base_y], fill=text_color)
    
    # رسم كفتي الميزان
    fg_draw.rectangle([left_scale_x, scale_y, left_scale_x + scale_width, scale_y + scale_height], fill=text_color)
    fg_draw.rectangle([right_scale_x, scale_y, right_scale_x + scale_width, scale_y + scale_height], fill=text_color)
    
    # رسم خيوط الميزان
    fg_draw.line([pole_x, size * 0.35, left_scale_x + scale_width//2, scale_y], fill=text_color, width=5)
    fg_draw.line([pole_x, size * 0.35, right_scale_x + scale_width//2, scale_y], fill=text_color, width=5)
    
    # إضافة النص للـ foreground
    fg_draw.text((text_x, text_y), text, fill=text_color, font=font)
    
    # حفظ الـ foreground
    fg_img.save('app_icon_foreground.png', 'PNG')
    
    print("✅ تم إنشاء الأيقونات بنجاح!")
    print("📁 الملفات المُنشأة:")
    print("   • app_icon.png (الأيقونة الأساسية)")
    print("   • app_icon_foreground.png (للأيقونة التكيفية)")

if __name__ == "__main__":
    create_app_icon()
