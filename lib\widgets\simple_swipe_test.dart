import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// مكون سحب مبسط للاختبار
class SimpleSwipeTest extends StatefulWidget {
  final Widget child;
  final VoidCallback onSwipe;
  final bool isFromCurrentUser;

  const SimpleSwipeTest({
    super.key,
    required this.child,
    required this.onSwipe,
    required this.isFromCurrentUser,
  });

  @override
  State<SimpleSwipeTest> createState() => _SimpleSwipeTestState();
}

class _SimpleSwipeTestState extends State<SimpleSwipeTest> {
  double _dragDistance = 0.0;
  bool _isTriggered = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.transparent,
      child: GestureDetector(
        behavior: HitTestBehavior.translucent,
        // إضافة النقر للرد
        onTap: () {
          if (kDebugMode) {
            print('👆 نقر للرد - تفعيل فوري!');
          }
          HapticFeedback.mediumImpact();
          widget.onSwipe();
        },
        onPanStart: (details) {
          if (kDebugMode) {
            print('🎯 بدء السحب البسيط');
          }
          setState(() {
            _dragDistance = 0.0;
            _isTriggered = false;
          });
        },
        onPanUpdate: (details) {
          final deltaX = details.delta.dx;

          if (kDebugMode) {
            print('📱 تحديث السحب: deltaX=$deltaX');
          }

          setState(() {
            _dragDistance += deltaX;
          });

          // تفعيل عند سحب 50 بكسل في أي اتجاه
          if (_dragDistance.abs() > 50 && !_isTriggered) {
            _isTriggered = true;
            HapticFeedback.mediumImpact();

            if (kDebugMode) {
              print('🎉 تم تفعيل السحب البسيط!');
            }

            widget.onSwipe();
          }
        },
        onPanEnd: (details) {
          if (kDebugMode) {
            print('🏁 انتهاء السحب البسيط');
          }
          setState(() {
            _dragDistance = 0.0;
            _isTriggered = false;
          });
        },
        child: Stack(
          children: [
            // أيقونة الرد - دائماً مرئية
            Positioned.fill(
              child: Container(
                color: Colors.red.withValues(
                  alpha: 0.1,
                ), // خلفية حمراء للاختبار
                child: Center(
                  child: Container(
                    width: 60,
                    height: 60,
                    decoration: const BoxDecoration(
                      color: Colors.blue,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.reply,
                      color: Colors.white,
                      size: 30,
                    ),
                  ),
                ),
              ),
            ),

            // الرسالة
            Transform.translate(
              offset: Offset(_dragDistance, 0.0),
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.green.withValues(
                    alpha: 0.1,
                  ), // خلفية خضراء للاختبار
                  border: Border.all(
                    color: Colors.blue.withValues(alpha: 0.3),
                    width: 2,
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Stack(
                  children: [
                    widget.child,
                    // مؤشر النقر
                    Positioned(
                      top: 4,
                      right: 4,
                      child: Container(
                        padding: const EdgeInsets.all(2),
                        decoration: const BoxDecoration(
                          color: Colors.blue,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.touch_app,
                          color: Colors.white,
                          size: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // مؤشر المسافة
            Positioned(
              top: 0,
              left: 0,
              child: Container(
                padding: const EdgeInsets.all(4),
                color: Colors.black.withValues(alpha: 0.7),
                child: Text(
                  'المسافة: ${_dragDistance.toStringAsFixed(1)}',
                  style: const TextStyle(color: Colors.white, fontSize: 12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// مكون اختبار شامل للسحب
class SwipeTestScreen extends StatefulWidget {
  const SwipeTestScreen({super.key});

  @override
  State<SwipeTestScreen> createState() => _SwipeTestScreenState();
}

class _SwipeTestScreenState extends State<SwipeTestScreen> {
  int _swipeCount = 0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار السحب'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // إحصائيات
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  Text(
                    'عدد مرات السحب: $_swipeCount',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'اسحب الرسائل أو انقر عليها للرد',
                    style: TextStyle(fontSize: 14),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 20),

            // رسالة من المستخدم الحالي
            SimpleSwipeTest(
              isFromCurrentUser: true,
              onSwipe: () {
                setState(() {
                  _swipeCount++;
                });
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('تم سحب رسالتك!'),
                    duration: Duration(seconds: 1),
                  ),
                );
              },
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                margin: const EdgeInsets.only(left: 50),
                decoration: BoxDecoration(
                  color: Colors.blue,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text(
                  'رسالة من المستخدم الحالي\n(اسحب أو انقر للرد)',
                  style: TextStyle(color: Colors.white),
                  textAlign: TextAlign.center,
                ),
              ),
            ),

            const SizedBox(height: 20),

            // رسالة من مستخدم آخر
            SimpleSwipeTest(
              isFromCurrentUser: false,
              onSwipe: () {
                setState(() {
                  _swipeCount++;
                });
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('تم سحب رسالة المستخدم الآخر!'),
                    duration: Duration(seconds: 1),
                  ),
                );
              },
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                margin: const EdgeInsets.only(right: 50),
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text(
                  'رسالة من مستخدم آخر\n(اسحب أو انقر للرد)',
                  style: TextStyle(color: Colors.black),
                  textAlign: TextAlign.center,
                ),
              ),
            ),

            const SizedBox(height: 20),

            // أزرار التحكم
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton(
                  onPressed: () {
                    setState(() {
                      _swipeCount = 0;
                    });
                  },
                  child: const Text('إعادة تعيين'),
                ),
                ElevatedButton(
                  onPressed: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('العدد الحالي: $_swipeCount'),
                        duration: const Duration(seconds: 2),
                      ),
                    );
                  },
                  child: const Text('عرض العدد'),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // تعليمات
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.yellow[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'تعليمات الاختبار:',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                  ),
                  SizedBox(height: 8),
                  Text('1. انقر على أي رسالة للرد الفوري'),
                  Text('2. أو اسحب الرسالة في أي اتجاه'),
                  Text('3. ستشعر باهتزاز عند التفعيل'),
                  Text('4. ستظهر رسالة تأكيد'),
                  Text('5. راقب العداد في الأعلى'),
                  Text('6. تحقق من الكونسول للرسائل التشخيصية'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
