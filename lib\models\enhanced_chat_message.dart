import 'package:cloud_firestore/cloud_firestore.dart';

/// نموذج رسالة محسن مع دعم pagination وإدارة الذاكرة
class EnhancedChatMessage {
  final String id;
  final String senderId;
  final String senderName;
  final String senderAvatar;
  final String content;
  final DateTime timestamp;
  final MessageType type;
  final bool isRead;
  final String? replyToId;
  final String? replyToMessageContent; // محتوى الرسالة المرجعية
  final String? replyToSenderName; // اسم مرسل الرسالة المرجعية
  final MessageType? replyToMessageType; // نوع الرسالة المرجعية
  final List<String> attachments;
  final Map<String, dynamic>? metadata;

  // خصائص pagination
  final int? sequenceNumber; // رقم تسلسلي للترتيب
  final String? batchId; // معرف المجموعة للتحميل التدريجي

  const EnhancedChatMessage({
    required this.id,
    required this.senderId,
    required this.senderName,
    required this.senderAvatar,
    required this.content,
    required this.timestamp,
    this.type = MessageType.text,
    this.isRead = false,
    this.replyToId,
    this.replyToMessageContent,
    this.replyToSenderName,
    this.replyToMessageType,
    this.attachments = const [],
    this.metadata,
    this.sequenceNumber,
    this.batchId,
  });

  /// إنشاء من Firebase Realtime Database
  factory EnhancedChatMessage.fromRealtimeDatabase(
    String id,
    Map<dynamic, dynamic> data,
  ) {
    return EnhancedChatMessage(
      id: id,
      senderId: data['senderId']?.toString() ?? '',
      senderName: data['senderName']?.toString() ?? 'مجهول',
      senderAvatar: data['senderAvatar']?.toString() ?? '',
      content: data['message']?.toString() ?? '',
      timestamp:
          data['timestamp'] != null
              ? DateTime.fromMillisecondsSinceEpoch(data['timestamp'])
              : DateTime.now(),
      type: _parseMessageType(data['type']),
      isRead: data['isRead'] == true,
      replyToId: data['replyToId']?.toString(),
      replyToMessageContent: data['replyToMessageContent']?.toString(),
      replyToSenderName: data['replyToSenderName']?.toString(),
      replyToMessageType: _parseMessageType(data['replyToMessageType']),
      attachments: _parseAttachments(data['attachments']),
      metadata:
          data['metadata'] != null
              ? Map<String, dynamic>.from(data['metadata'])
              : null,
      sequenceNumber: data['sequenceNumber'],
      batchId: data['batchId']?.toString(),
    );
  }

  /// إنشاء من Firestore
  factory EnhancedChatMessage.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    return EnhancedChatMessage(
      id: doc.id,
      senderId: data['senderId'] ?? '',
      senderName: data['senderName'] ?? 'مجهول',
      senderAvatar: data['senderAvatar'] ?? '',
      content: data['content'] ?? data['message'] ?? '',
      timestamp: (data['timestamp'] as Timestamp?)?.toDate() ?? DateTime.now(),
      type: _parseMessageType(data['type']),
      isRead: data['isRead'] == true,
      replyToId: data['replyToId'],
      replyToMessageContent: data['replyToMessageContent'],
      replyToSenderName: data['replyToSenderName'],
      replyToMessageType: _parseMessageType(data['replyToMessageType']),
      attachments: _parseAttachments(data['attachments']),
      metadata:
          data['metadata'] != null
              ? Map<String, dynamic>.from(data['metadata'])
              : null,
      sequenceNumber: data['sequenceNumber'],
      batchId: data['batchId'],
    );
  }

  /// تحويل إلى Map للحفظ
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'senderId': senderId,
      'senderName': senderName,
      'senderAvatar': senderAvatar,
      'content': content,
      'message': content, // للتوافق مع النظام القديم
      'timestamp': timestamp.millisecondsSinceEpoch,
      'type': type.toString().split('.').last,
      'isRead': isRead,
      'replyToId': replyToId,
      'replyToMessageContent': replyToMessageContent,
      'replyToSenderName': replyToSenderName,
      'replyToMessageType': replyToMessageType?.toString().split('.').last,
      'attachments': attachments,
      'metadata': metadata,
      'sequenceNumber': sequenceNumber,
      'batchId': batchId,
    };
  }

  /// إنشاء نسخة محدثة
  EnhancedChatMessage copyWith({
    String? id,
    String? senderId,
    String? senderName,
    String? senderAvatar,
    String? content,
    DateTime? timestamp,
    MessageType? type,
    bool? isRead,
    String? replyToId,
    String? replyToMessageContent,
    String? replyToSenderName,
    MessageType? replyToMessageType,
    List<String>? attachments,
    Map<String, dynamic>? metadata,
    int? sequenceNumber,
    String? batchId,
  }) {
    return EnhancedChatMessage(
      id: id ?? this.id,
      senderId: senderId ?? this.senderId,
      senderName: senderName ?? this.senderName,
      senderAvatar: senderAvatar ?? this.senderAvatar,
      content: content ?? this.content,
      timestamp: timestamp ?? this.timestamp,
      type: type ?? this.type,
      isRead: isRead ?? this.isRead,
      replyToId: replyToId ?? this.replyToId,
      replyToMessageContent:
          replyToMessageContent ?? this.replyToMessageContent,
      replyToSenderName: replyToSenderName ?? this.replyToSenderName,
      replyToMessageType: replyToMessageType ?? this.replyToMessageType,
      attachments: attachments ?? this.attachments,
      metadata: metadata ?? this.metadata,
      sequenceNumber: sequenceNumber ?? this.sequenceNumber,
      batchId: batchId ?? this.batchId,
    );
  }

  /// تحويل نوع الرسالة من String
  static MessageType _parseMessageType(dynamic type) {
    if (type == null) return MessageType.text;

    final typeStr = type.toString().toLowerCase();
    switch (typeStr) {
      case 'image':
        return MessageType.image;
      case 'file':
        return MessageType.file;
      case 'voice':
        return MessageType.voice;
      case 'video':
        return MessageType.video;
      default:
        return MessageType.text;
    }
  }

  /// تحويل المرفقات من dynamic
  static List<String> _parseAttachments(dynamic attachments) {
    if (attachments == null) return [];

    if (attachments is List) {
      return attachments.map((e) => e.toString()).toList();
    }

    return [];
  }

  /// التحقق من كون الرسالة حديثة (أقل من دقيقة)
  bool get isRecent {
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    return difference.inMinutes < 1;
  }

  /// التحقق من كون الرسالة من نفس اليوم
  bool get isToday {
    final now = DateTime.now();
    return timestamp.year == now.year &&
        timestamp.month == now.month &&
        timestamp.day == now.day;
  }

  /// الحصول على نص الوقت المنسق
  String get formattedTime {
    final hour = timestamp.hour.toString().padLeft(2, '0');
    final minute = timestamp.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  /// الحصول على نص التاريخ المنسق
  String get formattedDate {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays == 0) {
      return 'اليوم';
    } else if (difference.inDays == 1) {
      return 'أمس';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} أيام';
    } else {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    }
  }

  /// التحقق من كون الرسالة رد على رسالة أخرى
  bool get isReply => replyToId != null && replyToId!.isNotEmpty;

  /// الحصول على نص مختصر للرسالة المرجعية
  String get replyPreview {
    if (!isReply || replyToMessageContent == null) return '';

    final content = replyToMessageContent!;
    if (content.length <= 50) return content;
    return '${content.substring(0, 50)}...';
  }

  /// الحصول على أيقونة نوع الرسالة المرجعية
  String get replyTypeIcon {
    if (!isReply) return '';

    switch (replyToMessageType) {
      case MessageType.image:
        return '📷';
      case MessageType.file:
        return '📄';
      case MessageType.voice:
        return '🎵';
      case MessageType.video:
        return '🎥';
      case MessageType.system:
        return 'ℹ️';
      default:
        return '';
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is EnhancedChatMessage && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'EnhancedChatMessage(id: $id, senderId: $senderId, content: $content, timestamp: $timestamp)';
  }
}

/// أنواع الرسائل المدعومة
enum MessageType {
  text,
  image,
  file,
  voice,
  video,
  system, // رسائل النظام
}

/// نموذج مجموعة الرسائل للتحميل التدريجي
class MessageBatch {
  final String id;
  final List<EnhancedChatMessage> messages;
  final DateTime startTime;
  final DateTime endTime;
  final bool hasMore;
  final String? nextBatchId;

  const MessageBatch({
    required this.id,
    required this.messages,
    required this.startTime,
    required this.endTime,
    this.hasMore = false,
    this.nextBatchId,
  });

  /// دمج مجموعتين من الرسائل
  MessageBatch merge(MessageBatch other) {
    final allMessages = [...messages, ...other.messages];
    allMessages.sort((a, b) => a.timestamp.compareTo(b.timestamp));

    return MessageBatch(
      id: '$id+${other.id}',
      messages: allMessages,
      startTime:
          startTime.isBefore(other.startTime) ? startTime : other.startTime,
      endTime: endTime.isAfter(other.endTime) ? endTime : other.endTime,
      hasMore: other.hasMore,
      nextBatchId: other.nextBatchId,
    );
  }
}

/// حالة تحميل الرسائل
enum MessageLoadingState { initial, loading, loaded, loadingMore, error, empty }
