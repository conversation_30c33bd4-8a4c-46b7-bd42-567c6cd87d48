import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// مجموعة انيميشن للردود
class ReplyAnimations {
  
  /// انيميشن السحب للرد
  static AnimationController createSwipeController(TickerProvider vsync) {
    return AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: vsync,
    );
  }

  /// انيميشن ظهور معاينة الرد
  static AnimationController createReplyPreviewController(TickerProvider vsync) {
    return AnimationController(
      duration: const Duration(milliseconds: 250),
      vsync: vsync,
    );
  }

  /// انيميشن النقر على الرد
  static AnimationController createReplyTapController(TickerProvider vsync) {
    return AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: vsync,
    );
  }

  /// تأثير الاهتزاز للرد
  static void triggerReplyHaptic() {
    HapticFeedback.mediumImpact();
  }

  /// تأثير الاهتزاز للسحب
  static void triggerSwipeHaptic() {
    HapticFeedback.selectionClick();
  }

  /// تأثير الاهتزاز للإلغاء
  static void triggerCancelHaptic() {
    HapticFeedback.lightImpact();
  }
}

/// Widget للانيميشن المتقدم للردود
class AdvancedReplyAnimation extends StatefulWidget {
  final Widget child;
  final bool isVisible;
  final Duration duration;
  final Curve curve;

  const AdvancedReplyAnimation({
    super.key,
    required this.child,
    required this.isVisible,
    this.duration = const Duration(milliseconds: 300),
    this.curve = Curves.easeOutCubic,
  });

  @override
  State<AdvancedReplyAnimation> createState() => _AdvancedReplyAnimationState();
}

class _AdvancedReplyAnimationState extends State<AdvancedReplyAnimation>
    with SingleTickerProviderStateMixin {
  
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: widget.curve,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: widget.curve,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: widget.curve,
    ));

    if (widget.isVisible) {
      _controller.forward();
    }
  }

  @override
  void didUpdateWidget(AdvancedReplyAnimation oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isVisible != oldWidget.isVisible) {
      if (widget.isVisible) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: Transform.scale(
              scale: _scaleAnimation.value,
              child: widget.child,
            ),
          ),
        );
      },
    );
  }
}

/// Widget لتأثير الموجة عند الرد
class ReplyWaveEffect extends StatefulWidget {
  final bool isActive;
  final Color color;
  final double size;

  const ReplyWaveEffect({
    super.key,
    required this.isActive,
    this.color = const Color(0xFF6366F1),
    this.size = 50.0,
  });

  @override
  State<ReplyWaveEffect> createState() => _ReplyWaveEffectState();
}

class _ReplyWaveEffectState extends State<ReplyWaveEffect>
    with TickerProviderStateMixin {
  
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    ));
  }

  @override
  void didUpdateWidget(ReplyWaveEffect oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isActive && !oldWidget.isActive) {
      _controller.reset();
      _controller.forward();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          width: widget.size,
          height: widget.size,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(
              color: widget.color.withValues(
                alpha: (1.0 - _animation.value) * 0.6,
              ),
              width: 2.0,
            ),
          ),
          child: Transform.scale(
            scale: _animation.value * 2.0,
            child: Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: widget.color.withValues(
                  alpha: (1.0 - _animation.value) * 0.3,
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Widget لتأثير الضوء عند السحب
class SwipeGlowEffect extends StatelessWidget {
  final double progress;
  final bool isFromCurrentUser;
  final Color color;

  const SwipeGlowEffect({
    super.key,
    required this.progress,
    required this.isFromCurrentUser,
    this.color = const Color(0xFF6366F1),
  });

  @override
  Widget build(BuildContext context) {
    return Positioned.fill(
      child: Align(
        alignment: isFromCurrentUser 
            ? Alignment.centerRight 
            : Alignment.centerLeft,
        child: Container(
          width: 100 * progress,
          height: double.infinity,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: isFromCurrentUser 
                  ? Alignment.centerRight 
                  : Alignment.centerLeft,
              end: isFromCurrentUser 
                  ? Alignment.centerLeft 
                  : Alignment.centerRight,
              colors: [
                color.withValues(alpha: 0.1 * progress),
                color.withValues(alpha: 0.05 * progress),
                Colors.transparent,
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// Widget لتأثير النبض للرسالة المرجعية
class ReplyPulseEffect extends StatefulWidget {
  final Widget child;
  final bool isActive;

  const ReplyPulseEffect({
    super.key,
    required this.child,
    required this.isActive,
  });

  @override
  State<ReplyPulseEffect> createState() => _ReplyPulseEffectState();
}

class _ReplyPulseEffectState extends State<ReplyPulseEffect>
    with SingleTickerProviderStateMixin {
  
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _animation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void didUpdateWidget(ReplyPulseEffect oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isActive != oldWidget.isActive) {
      if (widget.isActive) {
        _controller.repeat(reverse: true);
      } else {
        _controller.stop();
        _controller.reset();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Transform.scale(
          scale: widget.isActive ? _animation.value : 1.0,
          child: widget.child,
        );
      },
    );
  }
}
