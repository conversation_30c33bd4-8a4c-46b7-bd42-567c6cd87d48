# The proguard configuration file for the following section is D:\20223\2025\legl92025\android\app\build\intermediates\aapt_proguard_file\release\processReleaseResources\aapt_rules.txt
-keep class android.app.Application { <init>(); }
-keep class androidx.core.app.CoreComponentFactory { <init>(); }
-keep class androidx.credentials.playservices.CredentialProviderMetadataHolder { <init>(); }
-keep class androidx.credentials.playservices.HiddenActivity { <init>(); }
-keep class androidx.profileinstaller.ProfileInstallReceiver { <init>(); }
-keep class androidx.startup.InitializationProvider { <init>(); }
-keep class com.google.android.datatransport.runtime.backends.TransportBackendDiscovery { <init>(); }
-keep class com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver { <init>(); }
-keep class com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService { <init>(); }
-keep class com.google.android.gms.auth.api.signin.RevocationBoundService { <init>(); }
-keep class com.google.android.gms.auth.api.signin.internal.SignInHubActivity { <init>(); }
-keep class com.google.android.gms.common.api.GoogleApiActivity { <init>(); }
-keep class com.google.android.gms.metadata.ModuleDependencies { <init>(); }
-keep class com.google.android.play.core.common.PlayCoreDialogWrapperActivity { <init>(); }
-keep class com.google.firebase.auth.internal.GenericIdpActivity { <init>(); }
-keep class com.google.firebase.auth.internal.RecaptchaActivity { <init>(); }
-keep class com.google.firebase.components.ComponentDiscoveryService { <init>(); }
-keep class com.google.firebase.iid.FirebaseInstanceIdReceiver { <init>(); }
-keep class com.google.firebase.messaging.FirebaseMessagingService { <init>(); }
-keep class com.google.firebase.provider.FirebaseInitProvider { <init>(); }
-keep class com.legal2025.yamy.MainActivity { <init>(); }
-keep class io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService { <init>(); }
-keep class io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider { <init>(); }
-keep class io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver { <init>(); }
-keep class io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService { <init>(); }
-keep class io.flutter.plugins.imagepicker.ImagePickerFileProvider { <init>(); }
-keep class io.flutter.plugins.urllauncher.WebViewActivity { <init>(); }
-keep class android.widget.Space { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.app.AlertController$RecycleListView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.view.menu.ActionMenuItemView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.view.menu.ExpandedMenuView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.view.menu.ListMenuItemView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionBarContainer { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionBarContextView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionBarOverlayLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionMenuView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActivityChooserView$InnerLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.AlertDialogLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ButtonBarLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ContentFrameLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.DialogTitle { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.FitWindowsFrameLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.FitWindowsLinearLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.SearchView$SearchAutoComplete { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.SwitchCompat { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.Toolbar { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ViewStubCompat { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.browser.browseractions.BrowserActionsFallbackMenuView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.core.widget.NestedScrollView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.preference.UnPressableLinearLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.preference.internal.PreferenceImageView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.recyclerview.widget.RecyclerView { <init>(android.content.Context, android.util.AttributeSet); }


# End of content from D:\20223\2025\legl92025\android\app\build\intermediates\aapt_proguard_file\release\processReleaseResources\aapt_rules.txt
# The proguard configuration file for the following section is D:\20223\2025\legl92025\android\app\build\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.7.0
# This is a configuration file for ProGuard.
# http://proguard.sourceforge.net/index.html#manual/usage.html
#
# Starting with version 2.2 of the Android plugin for Gradle, this file is distributed together with
# the plugin and unpacked at build-time. The files in $ANDROID_HOME are no longer maintained and
# will be ignored by new version of the Android plugin for Gradle.

# Optimizations: If you don't want to optimize, use the proguard-android.txt configuration file
# instead of this one, which turns off the optimization flags.
-allowaccessmodification

# Preserve some attributes that may be required for reflection.
-keepattributes AnnotationDefault,
                EnclosingMethod,
                InnerClasses,
                RuntimeVisibleAnnotations,
                RuntimeVisibleParameterAnnotations,
                RuntimeVisibleTypeAnnotations,
                Signature

-keep public class com.google.vending.licensing.ILicensingService
-keep public class com.android.vending.licensing.ILicensingService
-keep public class com.google.android.vending.licensing.ILicensingService
-dontnote com.android.vending.licensing.ILicensingService
-dontnote com.google.vending.licensing.ILicensingService
-dontnote com.google.android.vending.licensing.ILicensingService

# For native methods, see https://www.guardsquare.com/manual/configuration/examples#native
-keepclasseswithmembernames,includedescriptorclasses class * {
    native <methods>;
}

# Keep setters in Views so that animations can still work.
-keepclassmembers public class * extends android.view.View {
    void set*(***);
    *** get*();
}

# We want to keep methods in Activity that could be used in the XML attribute onClick.
-keepclassmembers class * extends android.app.Activity {
    public void *(android.view.View);
}

# For enumeration classes, see https://www.guardsquare.com/manual/configuration/examples#enumerations
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

-keepclassmembers class * implements android.os.Parcelable {
    public static final ** CREATOR;
}

# Preserve annotated Javascript interface methods.
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# The support libraries contains references to newer platform versions.
# Don't warn about those in case this app is linking against an older
# platform version. We know about them, and they are safe.
-dontnote android.support.**
-dontnote androidx.**
-dontwarn android.support.**
-dontwarn androidx.**

# Understand the @Keep support annotation.
-keep class android.support.annotation.Keep

-keep @android.support.annotation.Keep class * {*;}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <methods>;
}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <fields>;
}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <init>(...);
}

# These classes are duplicated between android.jar and org.apache.http.legacy.jar.
-dontnote org.apache.http.**
-dontnote android.net.http.**

# These classes are duplicated between android.jar and core-lambda-stubs.jar.
-dontnote java.lang.invoke.**

# End of content from D:\20223\2025\legl92025\android\app\build\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.7.0
# The proguard configuration file for the following section is C:\flutter\packages\flutter_tools\gradle\flutter_proguard_rules.pro
# Build the ephemeral app in a module project.
# Prevents: Warning: library class <plugin-package> depends on program class io.flutter.plugin.**
# This is due to plugins (libraries) depending on the embedding (the program jar)
-dontwarn io.flutter.plugin.**

# The android.** package is provided by the OS at runtime.
-dontwarn android.**

# In some cases, R8 is incorrectly stripping plugin classes. Keep
# all implementations of FlutterPlugin until we can determine
# why this is the case.
# See https://github.com/flutter/flutter/issues/154580.
-if class * implements io.flutter.embedding.engine.plugins.FlutterPlugin
-keep,allowshrinking,allowobfuscation class <1>

# End of content from C:\flutter\packages\flutter_tools\gradle\flutter_proguard_rules.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\proguard.txt
-dontwarn com.google.appengine.api.**
-dontwarn okio.**
-dontwarn org.apache.**
-dontwarn retrofit.android.**
-dontwarn retrofit.appengine.**
-dontwarn retrofit.client.**
-dontwarn rx.**

# This is necessary for keeping SecureTokenHttpApi and IdentityToolkitHttpApi
# Otherwise those classes get stripped out, as they are only being used
# reflectively.

-keepclasseswithmembernames interface * {
    @retrofit.http.* <methods>;
}

# This is necessary for parsing JSON responses, since the JSON converter uses reflection to figure out the class/type of response.
# We mainly need the *Response.classes to not be stripped out. All the firebase-auth classes are proguarded into "com.google.android.gms.internal.firebase-auth-api*".

-keep class com.google.android.gms.internal.** { *; }

# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.firebase-auth-api.zzaky {
  <fields>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb879d1880e441b3e772a8c480a3189c\transformed\jetified-firebase-firestore-25.1.4\proguard.txt
# Needed for DNS resolution.  Present in OpenJDK, but not Android
-dontwarn javax.naming.**

# Don't warn about checkerframework
#
# Guava uses the checkerframework and the annotations
# can safely be ignored at runtime.
-dontwarn org.checkerframework.**

# Guava warnings:
-dontwarn java.lang.ClassValue
-dontwarn com.google.j2objc.annotations.Weak
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement
-dontwarn javax.lang.model.element.Modifier

# Okhttp warnings.
-dontwarn okio.**
-dontwarn com.google.j2objc.annotations.**

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb879d1880e441b3e772a8c480a3189c\transformed\jetified-firebase-firestore-25.1.4\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_pdfview-1.4.1+1\android\build\intermediates\consumer_proguard_dir\release\exportReleaseConsumerProguardFiles\lib0\proguard.txt
-keep class com.shockwave.**
# End of content from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_pdfview-1.4.1+1\android\build\intermediates\consumer_proguard_dir\release\exportReleaseConsumerProguardFiles\lib0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_plugin_android_lifecycle-2.0.28\android\build\intermediates\consumer_proguard_dir\release\exportReleaseConsumerProguardFiles\lib0\proguard.txt
# The point of this package is to specify that a dependent plugin intends to
# use the AndroidX lifecycle classes. Make sure no R8 heuristics shrink classes
# brought in by the embedding's pom.
#
# This isn't strictly needed since by definition, plugins using Android
# lifecycles should implement DefaultLifecycleObserver and therefore keep it
# from being shrunk. But there seems to be an R8 bug so this needs to stay
# https://issuetracker.google.com/issues/142778206.
-keep class androidx.lifecycle.DefaultLifecycleObserver

# End of content from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_plugin_android_lifecycle-2.0.28\android\build\intermediates\consumer_proguard_dir\release\exportReleaseConsumerProguardFiles\lib0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62a9055d35d685d5b5521f555db2759b\transformed\media-1.1.0\proguard.txt
# Copyright (C) 2017 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Prevent Parcelable objects from being removed or renamed.
-keep class android.support.v4.media.** implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}

# Prevent Parcelable objects from being removed or renamed.
-keep class androidx.media.** implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}
# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62a9055d35d685d5b5521f555db2759b\transformed\media-1.1.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a78491001511f82c2595d244d8cb59d\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\proguard.txt
-if class androidx.credentials.CredentialManager
-keep class androidx.credentials.playservices.** {
  *;
}
# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a78491001511f82c2595d244d8cb59d\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b13dd6987e9417ee90818b32ba85674\transformed\preference-1.2.1\proguard.txt
# Copyright (C) 2015 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Preference objects are inflated via reflection
-keep public class androidx.preference.Preference {
    public <init>(android.content.Context, android.util.AttributeSet);
}
-keep public class * extends androidx.preference.Preference {
    public <init>(android.content.Context, android.util.AttributeSet);
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b13dd6987e9417ee90818b32ba85674\transformed\preference-1.2.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0252fce8a9272bd3faf4987742c89595\transformed\appcompat-1.1.0\proguard.txt
# Copyright (C) 2018 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Ensure that reflectively-loaded inflater is not obfuscated. This can be
# removed when we stop supporting AAPT1 builds.
-keepnames class androidx.appcompat.app.AppCompatViewInflater

# aapt is not able to read app::actionViewClass and app:actionProviderClass to produce proguard
# keep rules. Add a commonly used SearchView to the keep list until b/109831488 is resolved.
-keep class androidx.appcompat.widget.SearchView { <init>(...); }
# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0252fce8a9272bd3faf4987742c89595\transformed\appcompat-1.1.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2ad61dadf8aa2bd2b73469a4d1bbd6e\transformed\jetified-firebase-auth-interop-20.0.0\proguard.txt
# Can be removed once we pull in a dependency on firebase-common that includes
# https://github.com/firebase/firebase-android-sdk/pull/1472/commits/856f1ca1151cdd88679bbc778892f23dfa34fc06#diff-a2ed34b5a38b4c6c686b09e54865eb48
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2ad61dadf8aa2bd2b73469a4d1bbd6e\transformed\jetified-firebase-auth-interop-20.0.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f33407928b1ff45454297c1e1a94abf6\transformed\jetified-firebase-common-21.0.0\proguard.txt
-dontwarn com.google.firebase.platforminfo.KotlinDetector
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f33407928b1ff45454297c1e1a94abf6\transformed\jetified-firebase-common-21.0.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\092da85bee5dcbc65600606c104ce2b0\transformed\jetified-play-services-auth-base-18.0.10\proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.auth.zzev {
  <fields>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\092da85bee5dcbc65600606c104ce2b0\transformed\jetified-play-services-auth-base-18.0.10\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\251ae3e1e0bd6d03a4314eb3f1846e6d\transformed\jetified-recaptcha-18.6.1\proguard.txt
# Proguard cannot process META-INF/versions/9.
# See https://discuss.gradle.org/t/meta-inf-version-duplicate-error-when-using-proguard/31380
-dontwarn module-info

# Ignore the warning becuse ClassValueCtorCache is never used on Android.
-dontwarn kotlinx.coroutines.internal.ClassValueCtorCache

# Ignore warning due to the usage from Guava and kotlinx.coroutines.internal.ClassValueCtorCache
-dontwarn java.lang.ClassValue

# Ignore warning to accommodate the missing injar of kotlinx.coroutines.flow for
# androidx.slidingpanelayout.widget.
-dontwarn kotlinx.coroutines.flow.**

# This prevents the SDK to be obfuscated again when building the android app.
-keep class com.google.android.recaptcha.** { *; }

# This is required for recaptcha mobile to function properly.
# See: https://cloud.google.com/recaptcha-enterprise/docs/instrument-android-apps
-keep class com.google.android.play.core.integrity.** { *; }
-keep class com.google.android.gms.tasks.** {*;}

# To keep okhttp3 generated files which are used in our NetworkModule which is
# used widely across the app.
-dontwarn com.squareup.okhttp3.**
-dontwarn okhttp3.**
-keep class com.squareup.okhttp3.* { *;}
-keep class okhttp3.**
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.recaptcha.internal.zznd {
  <fields>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\251ae3e1e0bd6d03a4314eb3f1846e6d\transformed\jetified-recaptcha-18.6.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78235c1666807d8d32dff9b6755aeafd\transformed\jetified-play-services-fido-20.1.0\proguard.txt
# Methods enable and disable in this class are complained as unresolved
# references, but they are system APIs and are not used by Fido client apps.
-dontwarn android.nfc.NfcAdapter

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78235c1666807d8d32dff9b6755aeafd\transformed\jetified-play-services-fido-20.1.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f8b51821a67bbb5ffa84ff59d703db\transformed\jetified-play-services-base-18.1.0\proguard.txt
# b/35135904 Ensure that proguard will not strip the mResultGuardian.
-keepclassmembers class com.google.android.gms.common.api.internal.BasePendingResult {
  com.google.android.gms.common.api.internal.BasePendingResult$ReleasableResultGuardian mResultGuardian;
}



# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f8b51821a67bbb5ffa84ff59d703db\transformed\jetified-play-services-base-18.1.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e48a0da1324d1175a69c07227294e796\transformed\recyclerview-1.0.0\proguard.txt
# Copyright (C) 2015 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# When layoutManager xml attribute is used, RecyclerView inflates
#LayoutManagers' constructors using reflection.
-keep public class * extends androidx.recyclerview.widget.RecyclerView$LayoutManager {
    public <init>(android.content.Context, android.util.AttributeSet, int, int);
    public <init>();
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e48a0da1324d1175a69c07227294e796\transformed\recyclerview-1.0.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c4f8703bce2a2cd33ab5a16e4204943c\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\proguard.txt
-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.ViewModel {
    <init>(androidx.lifecycle.SavedStateHandle);
}

-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.AndroidViewModel {
    <init>(android.app.Application,androidx.lifecycle.SavedStateHandle);
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c4f8703bce2a2cd33ab5a16e4204943c\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\169da467914a6a1473350154d7e87c3f\transformed\lifecycle-viewmodel-2.7.0\proguard.txt
-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.ViewModel {
    <init>();
}

-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.AndroidViewModel {
    <init>(android.app.Application);
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\169da467914a6a1473350154d7e87c3f\transformed\lifecycle-viewmodel-2.7.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d5f1e97a552c04cf800b7b0ad4bccda\transformed\jetified-lifecycle-process-2.7.0\proguard.txt
# this rule is need to work properly when app is compiled with api 28, see b/142778206
-keepclassmembers class * extends androidx.lifecycle.EmptyActivityLifecycleCallbacks { *; }
# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d5f1e97a552c04cf800b7b0ad4bccda\transformed\jetified-lifecycle-process-2.7.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f8964b2ffb1dd03fb2ede3baaaa9eeb\transformed\lifecycle-runtime-2.7.0\proguard.txt
-keepattributes AnnotationDefault,
                RuntimeVisibleAnnotations,
                RuntimeVisibleParameterAnnotations,
                RuntimeVisibleTypeAnnotations

-keepclassmembers enum androidx.lifecycle.Lifecycle$Event {
    <fields>;
}

-keep class * implements androidx.lifecycle.GeneratedAdapter {
    <init>(...);
}

-keepclassmembers class ** {
    @androidx.lifecycle.OnLifecycleEvent *;
}

# this rule is need to work properly when app is compiled with api 28, see b/142778206
# Also this rule prevents registerIn from being inlined.
-keepclassmembers class androidx.lifecycle.ReportFragment$LifecycleCallbacks { *; }
# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f8964b2ffb1dd03fb2ede3baaaa9eeb\transformed\lifecycle-runtime-2.7.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5a01eea62fd6916d4890a530743a5e71\transformed\jetified-savedstate-1.2.1\proguard.txt
# Copyright (C) 2019 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

-keepclassmembers,allowobfuscation class * implements androidx.savedstate.SavedStateRegistry$AutoRecreated {
    <init>();
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5a01eea62fd6916d4890a530743a5e71\transformed\jetified-savedstate-1.2.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5246fa6856d03ebbc5d329a5f3859c1\transformed\jetified-window-1.2.0\proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# A rule that will keep classes that implement SidecarInterface$SidecarCallback if Sidecar seems
# be used. See b/157286362 and b/165268619 for details.
# TODO(b/208543178) investigate how to pass header jar to R8 so we don't need this rule
-if class androidx.window.layout.adapter.sidecar.SidecarCompat {
  public *** setExtensionCallback(androidx.window.layout.adapter.sidecar.ExtensionInterfaceCompat$ExtensionCallbackInterface);
}
-keep class androidx.window.layout.adapter.sidecar.SidecarCompat$TranslatingCallback,
 androidx.window.layout.adapter.sidecar.DistinctElementSidecarCallback {
  public *** onDeviceStateChanged(androidx.window.sidecar.SidecarDeviceState);
  public *** onWindowLayoutChanged(android.os.IBinder, androidx.window.sidecar.SidecarWindowLayoutInfo);
}
# Required for window area API reflection guard
-keep interface androidx.window.area.reflectionguard.* {*;}
# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5246fa6856d03ebbc5d329a5f3859c1\transformed\jetified-window-1.2.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5714b8491d21f656491d2c0c289421e4\transformed\rules\lib\META-INF\com.android.tools\r8-from-1.6.0\coroutines.pro
# Allow R8 to optimize away the FastServiceLoader.
# Together with ServiceLoader optimization in R8
# this results in direct instantiation when loading Dispatchers.Main
-assumenosideeffects class kotlinx.coroutines.internal.MainDispatcherLoader {
    boolean FAST_SERVICE_LOADER_ENABLED return false;
}

-assumenosideeffects class kotlinx.coroutines.internal.FastServiceLoaderKt {
    boolean ANDROID_DETECTED return true;
}

# Disable support for "Missing Main Dispatcher", since we always have Android main dispatcher
-assumenosideeffects class kotlinx.coroutines.internal.MainDispatchersKt {
    boolean SUPPORT_MISSING return false;
}

# Statically turn off all debugging facilities and assertions
-assumenosideeffects class kotlinx.coroutines.DebugKt {
    boolean getASSERTIONS_ENABLED() return false;
    boolean getDEBUG() return false;
    boolean getRECOVER_STACK_TRACES() return false;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5714b8491d21f656491d2c0c289421e4\transformed\rules\lib\META-INF\com.android.tools\r8-from-1.6.0\coroutines.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3a77a0c2e3a1f9a0f73f3968891045b6\transformed\rules\lib\META-INF\proguard\androidx.datastore_datastore-preferences-core.pro
-keepclassmembers class * extends androidx.datastore.preferences.protobuf.GeneratedMessageLite {
    <fields>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3a77a0c2e3a1f9a0f73f3968891045b6\transformed\rules\lib\META-INF\proguard\androidx.datastore_datastore-preferences-core.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bcf63ea068c252f9a5d11d9996b99815\transformed\rules\lib\META-INF\com.android.tools\r8\coroutines.pro
# When editing this file, update the following files as well:
# - META-INF/proguard/coroutines.pro
# - META-INF/com.android.tools/proguard/coroutines.pro

# Most of volatile fields are updated with AFU and should not be mangled
-keepclassmembers class kotlinx.coroutines.** {
    volatile <fields>;
}

# Same story for the standard library's SafeContinuation that also uses AtomicReferenceFieldUpdater
-keepclassmembers class kotlin.coroutines.SafeContinuation {
    volatile <fields>;
}

# These classes are only required by kotlinx.coroutines.debug.AgentPremain, which is only loaded when
# kotlinx-coroutines-core is used as a Java agent, so these are not needed in contexts where ProGuard is used.
-dontwarn java.lang.instrument.ClassFileTransformer
-dontwarn sun.misc.SignalHandler
-dontwarn java.lang.instrument.Instrumentation
-dontwarn sun.misc.Signal

# Only used in `kotlinx.coroutines.internal.ExceptionsConstructor`.
# The case when it is not available is hidden in a `try`-`catch`, as well as a check for Android.
-dontwarn java.lang.ClassValue

# An annotation used for build tooling, won't be directly accessed.
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement
# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bcf63ea068c252f9a5d11d9996b99815\transformed\rules\lib\META-INF\com.android.tools\r8\coroutines.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3db0e2fa8852c5dd218c4a37d3317c7b\transformed\jetified-play-services-tasks-18.2.0\proguard.txt


# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3db0e2fa8852c5dd218c4a37d3317c7b\transformed\jetified-play-services-tasks-18.2.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f4f54f2e8e5114b90f7f6b0790bbfac\transformed\jetified-play-services-basement-18.4.0\proguard.txt
# Needed when building against pre-Marshmallow SDK.
-dontwarn android.security.NetworkSecurityPolicy

# Needed when building against Marshmallow SDK.
-dontwarn android.app.Notification

# Protobuf has references not on the Android boot classpath
-dontwarn sun.misc.Unsafe
-dontwarn libcore.io.Memory

# Annotations used during internal SDK shrinking.
-dontwarn com.google.android.apps.common.proguard.UsedBy*
-dontwarn com.google.android.apps.common.proguard.SideEffectFree

# Annotations referenced by the SDK but whose definitions are contained in
# non-required dependencies.
-dontwarn javax.annotation.**
-dontwarn org.checkerframework.**
-dontwarn com.google.errorprone.annotations.**
-dontwarn org.jspecify.nullness.NullMarked

# Annotations no longer exist. Suppression prevents ProGuard failures in
# SDKs which depend on earlier versions of play-services-basement.
-dontwarn com.google.android.gms.common.util.VisibleForTesting

# Proguard flags for consumers of the Google Play services SDK
# https://developers.google.com/android/guides/setup#add_google_play_services_to_your_project

# Keep SafeParcelable NULL value, needed for reflection by DowngradeableSafeParcel
-keepclassmembers public class com.google.android.gms.common.internal.safeparcel.SafeParcelable {
    public static final *** NULL;
}

# Needed for Parcelable/SafeParcelable classes & their creators to not get renamed, as they are
# found via reflection.
-keep class com.google.android.gms.common.internal.ReflectedParcelable
-keepnames class * implements com.google.android.gms.common.internal.ReflectedParcelable
-keepclassmembers class * implements android.os.Parcelable {
  public static final *** CREATOR;
}

# Keep the classes/members we need for client functionality.
-keep @interface android.support.annotation.Keep
-keep @androidx.annotation.Keep class *
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <fields>;
}
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <methods>;
}

# Keep androidX equivalent of above android.support to allow Jetification.
-keep @interface androidx.annotation.Keep
-keep @androidx.annotation.Keep class *
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <fields>;
}
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <methods>;
}

# Keep the names of classes/members we need for client functionality.
-keep @interface com.google.android.gms.common.annotation.KeepName
-keepnames @com.google.android.gms.common.annotation.KeepName class *
-keepclassmembernames class * {
  @com.google.android.gms.common.annotation.KeepName *;
}

# Keep Dynamite API entry points
-keep @interface com.google.android.gms.common.util.DynamiteApi
-keep @com.google.android.gms.common.util.DynamiteApi public class * {
  public <fields>;
  public <methods>;
}



# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f4f54f2e8e5114b90f7f6b0790bbfac\transformed\jetified-play-services-basement-18.4.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e332f3db4188443f5d775516caac83bf\transformed\fragment-1.7.1\proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The default FragmentFactory creates Fragment instances using reflection
-if public class ** extends androidx.fragment.app.Fragment
-keepclasseswithmembers,allowobfuscation public class <1> {
    public <init>();
}

# FragmentTransition will reflectively lookup:
# androidx.transition.FragmentTransitionSupport
# We should ensure that we keep the constructor if the code using this is alive
-if class androidx.fragment.app.FragmentTransition {
   private static androidx.fragment.app.FragmentTransitionImpl resolveSupportImpl();
}
-keep class androidx.transition.FragmentTransitionSupport {
    public <init>();
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e332f3db4188443f5d775516caac83bf\transformed\fragment-1.7.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2954a6c971a58912a802c184cf9555b6\transformed\coordinatorlayout-1.0.0\proguard.txt
# Copyright (C) 2016 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# CoordinatorLayout resolves the behaviors of its child components with reflection.
-keep public class * extends androidx.coordinatorlayout.widget.CoordinatorLayout$Behavior {
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>();
}

# Make sure we keep annotations for CoordinatorLayout's DefaultBehavior and ViewPager's DecorView
-keepattributes *Annotation*

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2954a6c971a58912a802c184cf9555b6\transformed\coordinatorlayout-1.0.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\747ac75ee8d1f3c9197acadbeb20e355\transformed\transition-1.4.1\proguard.txt
# Copyright (C) 2017 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Keep a field in transition that is used to keep a reference to weakly-referenced object
-keepclassmembers class androidx.transition.ChangeBounds$* extends android.animation.AnimatorListenerAdapter {
  androidx.transition.ChangeBounds$ViewBounds mViewBounds;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\747ac75ee8d1f3c9197acadbeb20e355\transformed\transition-1.4.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4be2a370b3e1cc34c30c786725d4c6f2\transformed\vectordrawable-animated-1.1.0\proguard.txt
# Copyright (C) 2016 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# keep setters in VectorDrawables so that animations can still work.
-keepclassmembers class androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$* {
   void set*(***);
   *** get*();
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4be2a370b3e1cc34c30c786725d4c6f2\transformed\vectordrawable-animated-1.1.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cda5eae4219c760778cd3db22bb75c66\transformed\core-1.13.1\proguard.txt
# Never inline methods, but allow shrinking and obfuscation.
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.view.ViewCompat$Api* {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.view.WindowInsetsCompat$*Impl* {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.app.NotificationCompat$*$Api*Impl {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.os.UserHandleCompat$Api*Impl {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.widget.EdgeEffectCompat$Api*Impl {
  <methods>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cda5eae4219c760778cd3db22bb75c66\transformed\core-1.13.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d85966a571c96c4fc8ebc745f3849645\transformed\jetified-startup-runtime-1.1.1\proguard.txt
# It's important that we preserve initializer names, given they are used in the AndroidManifest.xml.
-keepnames class * extends androidx.startup.Initializer

# These Proguard rules ensures that ComponentInitializers are are neither shrunk nor obfuscated,
# and are a part of the primary dex file. This is because they are discovered and instantiated
# during application startup.
-keep class * extends androidx.startup.Initializer {
    # Keep the public no-argument constructor while allowing other methods to be optimized.
    <init>();
}

-assumenosideeffects class androidx.startup.StartupLogger { public static <methods>; }

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d85966a571c96c4fc8ebc745f3849645\transformed\jetified-startup-runtime-1.1.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a795b84807cce9fff096830a3c44a77\transformed\versionedparcelable-1.1.1\proguard.txt
-keep class * implements androidx.versionedparcelable.VersionedParcelable
-keep public class android.support.**Parcelizer { *; }
-keep public class androidx.**Parcelizer { *; }
-keep public class androidx.versionedparcelable.ParcelImpl

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a795b84807cce9fff096830a3c44a77\transformed\versionedparcelable-1.1.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b60911c716e05332d9329c3508646ea\transformed\jetified-firebase-components-18.0.0\proguard.txt
-dontwarn com.google.firebase.components.Component$Instantiation
-dontwarn com.google.firebase.components.Component$ComponentType

-keep class * implements com.google.firebase.components.ComponentRegistrar
-keep,allowshrinking interface com.google.firebase.components.ComponentRegistrar

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b60911c716e05332d9329c3508646ea\transformed\jetified-firebase-components-18.0.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bc67733266f25c9069d64d290f20b8c4\transformed\jetified-transport-backend-cct-3.1.9\proguard.txt
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bc67733266f25c9069d64d290f20b8c4\transformed\jetified-transport-backend-cct-3.1.9\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d749959293fd9250c69d136712ad7b3\transformed\jetified-firebase-encoders-json-18.0.0\proguard.txt

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d749959293fd9250c69d136712ad7b3\transformed\jetified-firebase-encoders-json-18.0.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87aeed8c3aae8080da720b443e433d19\transformed\jetified-transport-api-3.1.0\proguard.txt
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87aeed8c3aae8080da720b443e433d19\transformed\jetified-transport-api-3.1.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f7e31d6b2c1b9581873315c695df907\transformed\jetified-core-1.0.0\proguard.txt
# Copyright (C) 2022 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# These interfaces must be kept for the client library to invoke methods in extensions.
-keep interface androidx.window.extensions.core.util.function.Consumer {
  public void accept(***);
}
-keep interface androidx.window.extensions.core.util.function.Predicate {
  public boolean test(***);
}
-keep interface androidx.window.extensions.core.util.function.Function {
  public *** apply(***);
}
# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f7e31d6b2c1b9581873315c695df907\transformed\jetified-core-1.0.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1087fbe4b0f82d223fb3be8d518fbc23\transformed\rules\lib\META-INF\proguard\androidx-annotations.pro
-keep,allowobfuscation @interface androidx.annotation.Keep
-keep @androidx.annotation.Keep class * {*;}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <methods>;
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <fields>;
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <init>(...);
}

-keepclassmembers,allowobfuscation class * {
  @androidx.annotation.DoNotInline <methods>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1087fbe4b0f82d223fb3be8d518fbc23\transformed\rules\lib\META-INF\proguard\androidx-annotations.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a55fd7206bae11620ee3ce42a0254cb3\transformed\jetified-googleid-1.1.0\proguard.txt
# Proguard cannot process META-INF/versions/9.
# See https://discuss.gradle.org/t/meta-inf-version-duplicate-error-when-using-proguard/31380
-dontwarn module-info

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a55fd7206bae11620ee3ce42a0254cb3\transformed\jetified-googleid-1.1.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59dd47b3462a46e3afe35fa687cf2be7\transformed\rules\lib\META-INF\proguard\okio.pro
# Animal Sniffer compileOnly dependency to ensure APIs are compatible with older versions of Java.
-dontwarn org.codehaus.mojo.animal_sniffer.*

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59dd47b3462a46e3afe35fa687cf2be7\transformed\rules\lib\META-INF\proguard\okio.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f3cfa0e4156db2abdf30b887b52df56\transformed\jetified-protolite-well-known-types-18.0.1\proguard.txt
# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

# protobuf-javalite has a bug that requires this workaround rule
# https://github.com/protocolbuffers/protobuf/issues/6463#issuecomment-553183215
-keepclassmembers class * extends com.google.protobuf.GeneratedMessageLite {
  <fields>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f3cfa0e4156db2abdf30b887b52df56\transformed\jetified-protolite-well-known-types-18.0.1\proguard.txt
# The proguard configuration file for the following section is <unknown>

# End of content from <unknown>