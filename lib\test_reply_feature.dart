import 'package:flutter/foundation.dart';
import 'services/enhanced_chat_service.dart';
import 'models/enhanced_chat_message.dart';

/// اختبار شامل لميزة الرد على الرسائل
class ReplyFeatureTester {
  /// تشغيل جميع اختبارات الرد
  static Future<void> runAllReplyTests() async {
    if (!kDebugMode) {
      print('⚠️ اختبارات الرد تعمل فقط في وضع التطوير');
      return;
    }

    print('🔄 بدء اختبارات ميزة الرد');
    print('=' * 50);

    try {
      // اختبار إرسال رد
      await _testSendReply();

      // اختبار استقبال رد
      await _testReceiveReply();

      // اختبار عرض الردود
      await _testDisplayReplies();

      // اختبار البحث عن الرسائل
      await _testMessageSearch();

      // اختبار عدد الردود
      await _testReplyCount();

      print('=' * 50);
      print('✅ انتهت جميع اختبارات الرد بنجاح!');
    } catch (e) {
      print('❌ فشلت اختبارات الرد: $e');
    }
  }

  /// اختبار إرسال رد
  static Future<void> _testSendReply() async {
    print('\n📤 اختبار إرسال رد...');

    try {
      final testRoomId = 'test_reply_${DateTime.now().millisecondsSinceEpoch}';

      // إرسال رسالة أصلية
      final originalSuccess = await EnhancedChatService.sendMessage(
        chatRoomId: testRoomId,
        content: 'رسالة أصلية للاختبار',
      );

      if (!originalSuccess) {
        throw Exception('فشل في إرسال الرسالة الأصلية');
      }

      // الحصول على الرسالة الأصلية
      await Future.delayed(const Duration(milliseconds: 100));
      final messages = EnhancedChatService.getCachedMessages(testRoomId);

      if (messages.isEmpty) {
        throw Exception('لم يتم العثور على الرسالة الأصلية');
      }

      final originalMessage = messages.first;

      // إرسال رد
      final replySuccess = await EnhancedChatService.sendReply(
        chatRoomId: testRoomId,
        content: 'هذا رد على الرسالة الأصلية',
        replyToMessage: originalMessage,
      );

      if (replySuccess) {
        print('✅ تم إرسال الرد بنجاح');
      } else {
        throw Exception('فشل في إرسال الرد');
      }
    } catch (e) {
      print('❌ خطأ في اختبار إرسال الرد: $e');
      rethrow;
    }
  }

  /// اختبار استقبال رد
  static Future<void> _testReceiveReply() async {
    print('\n📥 اختبار استقبال رد...');

    try {
      // إنشاء رسالة مع رد
      final originalMessage = EnhancedChatMessage(
        id: 'original_${DateTime.now().millisecondsSinceEpoch}',
        senderId: 'user1',
        senderName: 'المستخدم الأول',
        senderAvatar: '',
        content: 'رسالة للرد عليها',
        timestamp: DateTime.now(),
      );

      // إنشاء رد
      final replyMessage = EnhancedChatMessage(
        id: 'reply_${DateTime.now().millisecondsSinceEpoch}',
        senderId: 'user2',
        senderName: 'المستخدم الثاني',
        senderAvatar: '',
        content: 'رد على الرسالة',
        timestamp: DateTime.now().add(const Duration(seconds: 1)),
        replyToId: originalMessage.id,
        replyToMessageContent: originalMessage.content,
        replyToSenderName: originalMessage.senderName,
        replyToMessageType: originalMessage.type,
      );

      // التحقق من خصائص الرد
      if (replyMessage.isReply) {
        print('✅ تم التعرف على الرسالة كرد');
      } else {
        throw Exception('لم يتم التعرف على الرسالة كرد');
      }

      if (replyMessage.replyPreview.isNotEmpty) {
        print('✅ معاينة الرد تعمل بشكل صحيح: "${replyMessage.replyPreview}"');
      } else {
        throw Exception('معاينة الرد فارغة');
      }
    } catch (e) {
      print('❌ خطأ في اختبار استقبال الرد: $e');
      rethrow;
    }
  }

  /// اختبار عرض الردود
  static Future<void> _testDisplayReplies() async {
    print('\n🖼️ اختبار عرض الردود...');

    try {
      // إنشاء رسائل مختلفة الأنواع للاختبار
      final textMessage = EnhancedChatMessage(
        id: 'text_msg',
        senderId: 'user1',
        senderName: 'مستخدم',
        senderAvatar: '',
        content: 'رسالة نصية',
        timestamp: DateTime.now(),
        type: MessageType.text,
      );

      final imageMessage = EnhancedChatMessage(
        id: 'image_msg',
        senderId: 'user1',
        senderName: 'مستخدم',
        senderAvatar: '',
        content: 'صورة',
        timestamp: DateTime.now(),
        type: MessageType.image,
      );

      final fileMessage = EnhancedChatMessage(
        id: 'file_msg',
        senderId: 'user1',
        senderName: 'مستخدم',
        senderAvatar: '',
        content: 'ملف',
        timestamp: DateTime.now(),
        type: MessageType.file,
      );

      // اختبار أيقونات الأنواع
      if (textMessage.replyTypeIcon.isEmpty) {
        print('✅ رسالة نصية: لا توجد أيقونة (صحيح)');
      }

      if (imageMessage.replyTypeIcon == '📷') {
        print('✅ رسالة صورة: أيقونة صحيحة');
      } else {
        throw Exception('أيقونة الصورة غير صحيحة');
      }

      if (fileMessage.replyTypeIcon == '📄') {
        print('✅ رسالة ملف: أيقونة صحيحة');
      } else {
        throw Exception('أيقونة الملف غير صحيحة');
      }
    } catch (e) {
      print('❌ خطأ في اختبار عرض الردود: $e');
      rethrow;
    }
  }

  /// اختبار البحث عن الرسائل
  static Future<void> _testMessageSearch() async {
    print('\n🔍 اختبار البحث عن الرسائل...');

    try {
      final testRoomId = 'test_search_${DateTime.now().millisecondsSinceEpoch}';

      // إرسال رسالة للبحث عنها
      await EnhancedChatService.sendMessage(
        chatRoomId: testRoomId,
        content: 'رسالة للبحث عنها',
      );

      await Future.delayed(const Duration(milliseconds: 100));

      // البحث في الـ cache
      final messages = EnhancedChatService.getCachedMessages(testRoomId);

      if (messages.isNotEmpty) {
        final messageId = messages.first.id;

        // البحث عن الرسالة
        final foundMessage = EnhancedChatService.findMessageInCache(
          testRoomId,
          messageId,
        );

        if (foundMessage != null) {
          print('✅ تم العثور على الرسالة في الـ cache');
        } else {
          throw Exception('لم يتم العثور على الرسالة في الـ cache');
        }

        // اختبار البحث عن رسالة غير موجودة
        final notFoundMessage = EnhancedChatService.findMessageInCache(
          testRoomId,
          'non_existent_id',
        );

        if (notFoundMessage == null) {
          print('✅ البحث عن رسالة غير موجودة يعيد null (صحيح)');
        } else {
          throw Exception('البحث عن رسالة غير موجودة لم يعيد null');
        }
      }
    } catch (e) {
      print('❌ خطأ في اختبار البحث عن الرسائل: $e');
      rethrow;
    }
  }

  /// اختبار عدد الردود
  static Future<void> _testReplyCount() async {
    print('\n🔢 اختبار عدد الردود...');

    try {
      final testRoomId = 'test_count_${DateTime.now().millisecondsSinceEpoch}';

      // إرسال رسالة أصلية
      await EnhancedChatService.sendMessage(
        chatRoomId: testRoomId,
        content: 'رسالة أصلية',
      );

      await Future.delayed(const Duration(milliseconds: 100));

      final messages = EnhancedChatService.getCachedMessages(testRoomId);

      if (messages.isNotEmpty) {
        final originalMessage = messages.first;

        // إرسال عدة ردود
        for (int i = 1; i <= 3; i++) {
          await EnhancedChatService.sendReply(
            chatRoomId: testRoomId,
            content: 'رد رقم $i',
            replyToMessage: originalMessage,
          );
          await Future.delayed(const Duration(milliseconds: 50));
        }

        // عد الردود
        final replyCount = EnhancedChatService.getReplyCount(
          testRoomId,
          originalMessage.id,
        );

        if (replyCount == 3) {
          print('✅ عدد الردود صحيح: $replyCount');
        } else {
          throw Exception('عدد الردود غير صحيح: $replyCount (متوقع: 3)');
        }

        // الحصول على قائمة الردود
        final replies = EnhancedChatService.getRepliesForMessage(
          testRoomId,
          originalMessage.id,
        );

        if (replies.length == 3) {
          print('✅ قائمة الردود صحيحة: ${replies.length} ردود');
        } else {
          throw Exception(
            'قائمة الردود غير صحيحة: ${replies.length} (متوقع: 3)',
          );
        }
      }
    } catch (e) {
      print('❌ خطأ في اختبار عدد الردود: $e');
      rethrow;
    }
  }

  /// اختبار سريع للرد
  static Future<void> quickReplyTest() async {
    if (!kDebugMode) return;

    print('🏃‍♂️ اختبار سريع للرد...');

    try {
      final testRoomId = 'quick_reply_${DateTime.now().millisecondsSinceEpoch}';

      // إرسال رسالة أصلية
      await EnhancedChatService.sendMessage(
        chatRoomId: testRoomId,
        content: 'رسالة للرد السريع',
      );

      await Future.delayed(const Duration(milliseconds: 100));

      final messages = EnhancedChatService.getCachedMessages(testRoomId);

      if (messages.isNotEmpty) {
        // إرسال رد
        final success = await EnhancedChatService.sendReply(
          chatRoomId: testRoomId,
          content: 'رد سريع',
          replyToMessage: messages.first,
        );

        if (success) {
          print('✅ الاختبار السريع للرد نجح');
        } else {
          print('❌ الاختبار السريع للرد فشل');
        }
      }
    } catch (e) {
      print('❌ خطأ في الاختبار السريع للرد: $e');
    }
  }
}

/// دالة مساعدة لتشغيل اختبارات الرد
Future<void> runReplyTests() async {
  await ReplyFeatureTester.runAllReplyTests();
}

/// دالة مساعدة للاختبار السريع
Future<void> runQuickReplyTest() async {
  await ReplyFeatureTester.quickReplyTest();
}
