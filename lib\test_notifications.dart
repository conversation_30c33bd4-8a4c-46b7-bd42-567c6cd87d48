import 'package:flutter/foundation.dart';
import 'services/notification_service.dart';

/// اختبار نظام الإشعارات
class NotificationTester {
  /// اختبار إرسال إشعار ملف جديد
  static Future<void> testNewFileNotification() async {
    try {
      if (kDebugMode) {
        print('🧪 اختبار إرسال إشعار ملف جديد...');
      }

      await NotificationService.sendNewFileNotification(
        fileName: 'اختبار_ملف_جديد.pdf',
        subjectName: 'الفقه الإسلامي',
        academicYear: 'الفرقة الأولى',
        category: 'أسئلة',
        fileUrl: 'https://example.com/test.pdf',
      );

      if (kDebugMode) {
        print('✅ تم إرسال إشعار الاختبار بنجاح');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في اختبار الإشعار: $e');
      }
    }
  }

  /// اختبار إرسال إشعارات لجميع الفرق
  static Future<void> testAllAcademicYears() async {
    final academicYears = [
      'الفرقة الأولى',
      'الفرقة الثانية',
      'الفرقة الثالثة',
      'الفرقة الرابعة',
    ];

    for (final year in academicYears) {
      try {
        if (kDebugMode) {
          print('🧪 اختبار إشعار للفرقة: $year');
        }

        await NotificationService.sendNewFileNotification(
          fileName: 'اختبار_$year.pdf',
          subjectName: 'مادة اختبار',
          academicYear: year,
          category: 'اختبار',
          fileUrl: 'https://example.com/test_$year.pdf',
        );

        if (kDebugMode) {
          print('✅ تم إرسال إشعار للفرقة: $year');
        }

        // تأخير قصير بين الإشعارات
        await Future.delayed(const Duration(seconds: 1));
      } catch (e) {
        if (kDebugMode) {
          print('❌ خطأ في إرسال إشعار للفرقة $year: $e');
        }
      }
    }
  }

  /// اختبار تنظيف الإشعارات القديمة
  static Future<void> testCleanupOldNotifications() async {
    try {
      if (kDebugMode) {
        print('🧪 اختبار تنظيف الإشعارات القديمة...');
      }

      // إرسال عدة إشعارات للاختبار
      for (int i = 1; i <= 10; i++) {
        await NotificationService.sendNewFileNotification(
          fileName: 'اختبار_تنظيف_$i.pdf',
          subjectName: 'مادة اختبار',
          academicYear: 'الفرقة الأولى',
          category: 'اختبار',
          fileUrl: 'https://example.com/cleanup_test_$i.pdf',
        );

        // تأخير قصير
        await Future.delayed(const Duration(milliseconds: 500));
      }

      if (kDebugMode) {
        print('✅ تم إرسال 10 إشعارات للاختبار');
        print('🗑️ سيتم الاحتفاظ بآخر 5 إشعارات فقط');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في اختبار تنظيف الإشعارات: $e');
      }
    }
  }

  /// تشغيل جميع الاختبارات
  static Future<void> runAllTests() async {
    if (kDebugMode) {
      print('🚀 بدء اختبارات نظام الإشعارات...');
      print('=' * 50);
    }

    // اختبار إشعار واحد
    await testNewFileNotification();
    await Future.delayed(const Duration(seconds: 2));

    // اختبار جميع الفرق
    await testAllAcademicYears();
    await Future.delayed(const Duration(seconds: 2));

    // اختبار تنظيف الإشعارات
    await testCleanupOldNotifications();

    if (kDebugMode) {
      print('=' * 50);
      print('✅ انتهت جميع اختبارات نظام الإشعارات');
    }
  }
}

/// دالة مساعدة لتشغيل الاختبارات من أي مكان
Future<void> runNotificationTests() async {
  await NotificationTester.runAllTests();
}
