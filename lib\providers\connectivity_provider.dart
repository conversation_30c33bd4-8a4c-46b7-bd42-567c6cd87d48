import 'dart:async';
import 'package:flutter/foundation.dart';
import '../services/connectivity_service.dart';

/// مزود حالة الاتصال
class ConnectivityProvider extends ChangeNotifier {
  final ConnectivityService _connectivityService = ConnectivityService();
  
  bool _isConnected = true;
  StreamSubscription<bool>? _connectionSubscription;
  
  /// الحصول على حالة الاتصال
  bool get isConnected => _isConnected;
  
  /// تهيئة المزود
  Future<void> initialize() async {
    try {
      await _connectivityService.initialize();
      _isConnected = _connectivityService.isConnected;
      
      // الاستماع لتغييرات الاتصال
      _connectionSubscription = _connectivityService.connectionStream.listen((connected) {
        if (_isConnected != connected) {
          _isConnected = connected;
          notifyListeners();
        }
      });
      
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تهيئة مزود الاتصال: $e');
    }
  }
  
  @override
  void dispose() {
    _connectionSubscription?.cancel();
    super.dispose();
  }
}
