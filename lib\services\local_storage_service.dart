import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:path_provider/path_provider.dart';
import '../models/chat_message.dart';
import '../models/pdf_file.dart';
import '../models/user_data.dart';

/// خدمة التخزين المحلي باستخدام Hive
class LocalStorageService {
  static final LocalStorageService _instance = LocalStorageService._internal();
  factory LocalStorageService() => _instance;
  LocalStorageService._internal();

  // أسماء الصناديق
  static const String _messagesBox = 'messages';
  static const String _pdfFilesBox = 'pdf_files';
  static const String _userDataBox = 'user_data';
  static const String _settingsBox = 'settings';
  static const String _cacheBox = 'cache';

  bool _isInitialized = false;

  /// تهيئة نظام التخزين المحلي
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // تهيئة Hive
      await Hive.initFlutter();

      // تسجيل المحولات (Adapters)
      if (!Hive.isAdapterRegistered(0)) {
        Hive.registerAdapter(ChatMessageAdapter());
      }
      if (!Hive.isAdapterRegistered(1)) {
        Hive.registerAdapter(PdfFileAdapter());
      }
      if (!Hive.isAdapterRegistered(2)) {
        Hive.registerAdapter(UserDataAdapter());
      }

      // فتح الصناديق
      await _openBoxes();

      _isInitialized = true;
      debugPrint('✅ تم تهيئة نظام التخزين المحلي بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة التخزين المحلي: $e');
      rethrow;
    }
  }

  /// فتح جميع الصناديق المطلوبة
  Future<void> _openBoxes() async {
    await Future.wait([
      Hive.openBox<ChatMessage>(_messagesBox),
      Hive.openBox<PdfFile>(_pdfFilesBox),
      Hive.openBox<UserData>(_userDataBox),
      Hive.openBox(_settingsBox),
      Hive.openBox(_cacheBox),
    ]);
  }

  /// الحصول على صندوق الرسائل
  Box<ChatMessage> get _messages => Hive.box<ChatMessage>(_messagesBox);

  /// الحصول على صندوق ملفات PDF
  Box<PdfFile> get _pdfFiles => Hive.box<PdfFile>(_pdfFilesBox);

  /// الحصول على صندوق بيانات المستخدم
  Box<UserData> get _userData => Hive.box<UserData>(_userDataBox);

  /// الحصول على صندوق الإعدادات
  Box get _settings => Hive.box(_settingsBox);

  /// الحصول على صندوق التخزين المؤقت
  Box get _cache => Hive.box(_cacheBox);

  // ==================== إدارة الرسائل ====================

  /// حفظ رسالة محلياً
  Future<void> saveMessage(ChatMessage message) async {
    try {
      await _messages.put(message.id, message);
      debugPrint('💾 تم حفظ الرسالة محلياً: ${message.id}');
    } catch (e) {
      debugPrint('❌ خطأ في حفظ الرسالة: $e');
    }
  }

  /// حفظ عدة رسائل
  Future<void> saveMessages(List<ChatMessage> messages) async {
    try {
      final Map<String, ChatMessage> messageMap = {
        for (var message in messages) message.id: message
      };
      await _messages.putAll(messageMap);
      debugPrint('💾 تم حفظ ${messages.length} رسالة محلياً');
    } catch (e) {
      debugPrint('❌ خطأ في حفظ الرسائل: $e');
    }
  }

  /// الحصول على رسائل غرفة معينة
  List<ChatMessage> getMessagesForRoom(String roomId) {
    try {
      return _messages.values
          .where((message) => message.roomId == roomId)
          .toList()
        ..sort((a, b) => b.timestamp.compareTo(a.timestamp));
    } catch (e) {
      debugPrint('❌ خطأ في جلب رسائل الغرفة: $e');
      return [];
    }
  }

  /// الحصول على آخر رسائل غرفة (مع تحديد العدد)
  List<ChatMessage> getLatestMessagesForRoom(String roomId, {int limit = 50}) {
    try {
      final messages = getMessagesForRoom(roomId);
      return messages.take(limit).toList();
    } catch (e) {
      debugPrint('❌ خطأ في جلب آخر الرسائل: $e');
      return [];
    }
  }

  /// حذف رسالة
  Future<void> deleteMessage(String messageId) async {
    try {
      await _messages.delete(messageId);
      debugPrint('🗑️ تم حذف الرسالة: $messageId');
    } catch (e) {
      debugPrint('❌ خطأ في حذف الرسالة: $e');
    }
  }

  /// حذف جميع رسائل غرفة
  Future<void> deleteMessagesForRoom(String roomId) async {
    try {
      final messagesToDelete = _messages.values
          .where((message) => message.roomId == roomId)
          .map((message) => message.id)
          .toList();

      for (final messageId in messagesToDelete) {
        await _messages.delete(messageId);
      }
      debugPrint('🗑️ تم حذف ${messagesToDelete.length} رسالة من الغرفة: $roomId');
    } catch (e) {
      debugPrint('❌ خطأ في حذف رسائل الغرفة: $e');
    }
  }

  /// الحصول على الرسائل غير المرسلة (للمزامنة)
  List<ChatMessage> getUnsentMessages() {
    try {
      return _messages.values
          .where((message) => !message.isSent)
          .toList()
        ..sort((a, b) => a.timestamp.compareTo(b.timestamp));
    } catch (e) {
      debugPrint('❌ خطأ في جلب الرسائل غير المرسلة: $e');
      return [];
    }
  }

  /// تحديث حالة إرسال الرسالة
  Future<void> markMessageAsSent(String messageId) async {
    try {
      final message = _messages.get(messageId);
      if (message != null) {
        final updatedMessage = message.copyWith(isSent: true);
        await _messages.put(messageId, updatedMessage);
        debugPrint('✅ تم تحديث حالة الرسالة كمرسلة: $messageId');
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحديث حالة الرسالة: $e');
    }
  }

  // ==================== إدارة ملفات PDF ====================

  /// حفظ ملف PDF محلياً
  Future<void> savePdfFile(PdfFile pdfFile) async {
    try {
      await _pdfFiles.put(pdfFile.id, pdfFile);
      debugPrint('📄 تم حفظ ملف PDF محلياً: ${pdfFile.name}');
    } catch (e) {
      debugPrint('❌ خطأ في حفظ ملف PDF: $e');
    }
  }

  /// الحصول على جميع ملفات PDF المحفوظة
  List<PdfFile> getAllPdfFiles() {
    try {
      return _pdfFiles.values.toList()
        ..sort((a, b) => b.downloadDate.compareTo(a.downloadDate));
    } catch (e) {
      debugPrint('❌ خطأ في جلب ملفات PDF: $e');
      return [];
    }
  }

  /// الحصول على ملفات PDF لموضوع معين
  List<PdfFile> getPdfFilesForSubject(String subject) {
    try {
      return _pdfFiles.values
          .where((pdf) => pdf.subject == subject)
          .toList()
        ..sort((a, b) => b.downloadDate.compareTo(a.downloadDate));
    } catch (e) {
      debugPrint('❌ خطأ في جلب ملفات PDF للموضوع: $e');
      return [];
    }
  }

  /// حذف ملف PDF
  Future<void> deletePdfFile(String pdfId) async {
    try {
      final pdfFile = _pdfFiles.get(pdfId);
      if (pdfFile != null) {
        // حذف الملف من النظام
        if (pdfFile.localPath != null) {
          final file = File(pdfFile.localPath!);
          if (await file.exists()) {
            await file.delete();
          }
        }
        // حذف من قاعدة البيانات
        await _pdfFiles.delete(pdfId);
        debugPrint('🗑️ تم حذف ملف PDF: ${pdfFile.name}');
      }
    } catch (e) {
      debugPrint('❌ خطأ في حذف ملف PDF: $e');
    }
  }

  // ==================== إدارة بيانات المستخدم ====================

  /// حفظ بيانات المستخدم
  Future<void> saveUserData(UserData userData) async {
    try {
      await _userData.put('current_user', userData);
      debugPrint('👤 تم حفظ بيانات المستخدم محلياً');
    } catch (e) {
      debugPrint('❌ خطأ في حفظ بيانات المستخدم: $e');
    }
  }

  /// الحصول على بيانات المستخدم
  UserData? getUserData() {
    try {
      return _userData.get('current_user');
    } catch (e) {
      debugPrint('❌ خطأ في جلب بيانات المستخدم: $e');
      return null;
    }
  }

  /// حذف بيانات المستخدم
  Future<void> clearUserData() async {
    try {
      await _userData.clear();
      debugPrint('🗑️ تم حذف بيانات المستخدم');
    } catch (e) {
      debugPrint('❌ خطأ في حذف بيانات المستخدم: $e');
    }
  }

  // ==================== إدارة الإعدادات ====================

  /// حفظ إعداد
  Future<void> saveSetting(String key, dynamic value) async {
    try {
      await _settings.put(key, value);
    } catch (e) {
      debugPrint('❌ خطأ في حفظ الإعداد: $e');
    }
  }

  /// الحصول على إعداد
  T? getSetting<T>(String key, {T? defaultValue}) {
    try {
      return _settings.get(key, defaultValue: defaultValue) as T?;
    } catch (e) {
      debugPrint('❌ خطأ في جلب الإعداد: $e');
      return defaultValue;
    }
  }

  // ==================== إدارة التخزين المؤقت ====================

  /// حفظ في التخزين المؤقت
  Future<void> cacheData(String key, dynamic data, {Duration? expiry}) async {
    try {
      final cacheItem = {
        'data': data,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'expiry': expiry?.inMilliseconds,
      };
      await _cache.put(key, cacheItem);
    } catch (e) {
      debugPrint('❌ خطأ في حفظ البيانات المؤقتة: $e');
    }
  }

  /// الحصول من التخزين المؤقت
  T? getCachedData<T>(String key) {
    try {
      final cacheItem = _cache.get(key);
      if (cacheItem == null) return null;

      final timestamp = cacheItem['timestamp'] as int;
      final expiry = cacheItem['expiry'] as int?;

      // فحص انتهاء الصلاحية
      if (expiry != null) {
        final now = DateTime.now().millisecondsSinceEpoch;
        if (now - timestamp > expiry) {
          _cache.delete(key);
          return null;
        }
      }

      return cacheItem['data'] as T?;
    } catch (e) {
      debugPrint('❌ خطأ في جلب البيانات المؤقتة: $e');
      return null;
    }
  }

  // ==================== إدارة عامة ====================

  /// الحصول على إحصائيات التخزين
  Map<String, dynamic> getStorageStats() {
    return {
      'messages_count': _messages.length,
      'pdf_files_count': _pdfFiles.length,
      'cache_items_count': _cache.length,
      'storage_initialized': _isInitialized,
    };
  }

  /// تنظيف البيانات القديمة
  Future<void> cleanupOldData() async {
    try {
      // تنظيف الرسائل القديمة (أكثر من 30 يوم)
      final cutoffDate = DateTime.now().subtract(const Duration(days: 30));
      final oldMessages = _messages.values
          .where((message) => message.timestamp.isBefore(cutoffDate))
          .map((message) => message.id)
          .toList();

      for (final messageId in oldMessages) {
        await _messages.delete(messageId);
      }

      // تنظيف التخزين المؤقت المنتهي الصلاحية
      final expiredCacheKeys = <String>[];
      for (final key in _cache.keys) {
        final cacheItem = _cache.get(key);
        if (cacheItem != null) {
          final timestamp = cacheItem['timestamp'] as int;
          final expiry = cacheItem['expiry'] as int?;
          if (expiry != null) {
            final now = DateTime.now().millisecondsSinceEpoch;
            if (now - timestamp > expiry) {
              expiredCacheKeys.add(key);
            }
          }
        }
      }

      for (final key in expiredCacheKeys) {
        await _cache.delete(key);
      }

      debugPrint('🧹 تم تنظيف ${oldMessages.length} رسالة قديمة و ${expiredCacheKeys.length} عنصر مؤقت');
    } catch (e) {
      debugPrint('❌ خطأ في تنظيف البيانات القديمة: $e');
    }
  }

  /// إغلاق جميع الصناديق
  Future<void> dispose() async {
    try {
      await Hive.close();
      _isInitialized = false;
      debugPrint('🔒 تم إغلاق نظام التخزين المحلي');
    } catch (e) {
      debugPrint('❌ خطأ في إغلاق التخزين المحلي: $e');
    }
  }
}
