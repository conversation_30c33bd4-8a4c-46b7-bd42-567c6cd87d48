import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'dart:async';
import '../models/enhanced_chat_message.dart';
import '../models/chat_model.dart';
import '../services/enhanced_chat_service.dart';
import '../providers/theme_provider.dart';
import '../providers/auth_provider.dart';
import '../widgets/pagination_loading_widget.dart';
import '../widgets/swipe_to_reply_widget.dart';
import '../widgets/reply_message_widget.dart';
import '../widgets/simple_swipe_test.dart';
import '../utils/chat_performance_manager.dart';

class EnhancedChatRoomScreen extends StatefulWidget {
  final ChatRoomModel chatRoom;

  const EnhancedChatRoomScreen({super.key, required this.chatRoom});

  @override
  State<EnhancedChatRoomScreen> createState() => _EnhancedChatRoomScreenState();
}

class _EnhancedChatRoomScreenState extends State<EnhancedChatRoomScreen>
    with TickerProviderStateMixin {
  // Controllers
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final FocusNode _messageFocusNode = FocusNode();

  // Animation Controllers
  late AnimationController _slideAnimationController;
  late AnimationController _fadeAnimationController;
  late AnimationController _messageAnimationController;

  // Animations
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _messageAnimation;

  // State
  List<EnhancedChatMessage> _messages = [];
  MessageLoadingState _loadingState = MessageLoadingState.initial;
  bool _hasMoreMessages = false;
  bool _isLoadingMore = false;
  bool _isTyping = false;
  Timer? _typingTimer;

  // Pagination
  DateTime? _oldestMessageTimestamp;
  static const double _loadMoreThreshold = 100.0; // المسافة للتحميل التلقائي

  // Reply functionality
  EnhancedChatMessage? _replyToMessage;
  bool _isReplying = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeScrollListener();
    _loadInitialMessages();
    _startListeningToMessages();

    // تسجيل الغرفة في مدير الأداء
    ChatPerformanceManager().registerRoom(widget.chatRoom.id);
    ChatPerformanceManager().optimizeActiveRoom(widget.chatRoom.id);
  }

  @override
  void dispose() {
    // إلغاء تسجيل الغرفة من مدير الأداء
    ChatPerformanceManager().unregisterRoom(widget.chatRoom.id);

    _slideAnimationController.dispose();
    _fadeAnimationController.dispose();
    _messageAnimationController.dispose();
    _messageController.dispose();
    _scrollController.dispose();
    _messageFocusNode.dispose();
    _typingTimer?.cancel();
    super.dispose();
  }

  void _initializeAnimations() {
    // انتقال الشاشة من اليمين
    _slideAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _slideAnimationController,
        curve: Curves.easeOutCubic,
      ),
    );

    // تلاشي الشاشة
    _fadeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeAnimationController, curve: Curves.easeIn),
    );

    // انيميشن الرسائل
    _messageAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _messageAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _messageAnimationController,
        curve: Curves.easeOut,
      ),
    );

    // بدء الانيميشن
    _slideAnimationController.forward();
    _fadeAnimationController.forward();
  }

  void _initializeScrollListener() {
    _scrollController.addListener(() {
      // التحميل التلقائي عند الوصول للأعلى
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent - _loadMoreThreshold) {
        if (!_isLoadingMore && _hasMoreMessages) {
          _loadMoreMessages();
        }
      }
    });
  }

  Future<void> _loadInitialMessages() async {
    setState(() {
      _loadingState = MessageLoadingState.loading;
    });

    try {
      final batch = await EnhancedChatService.loadInitialMessages(
        widget.chatRoom.id,
      );

      setState(() {
        _messages = batch.messages;
        _hasMoreMessages = batch.hasMore;
        _oldestMessageTimestamp =
            batch.messages.isNotEmpty ? batch.messages.last.timestamp : null;
        _loadingState =
            batch.messages.isEmpty
                ? MessageLoadingState.empty
                : MessageLoadingState.loaded;
      });

      // انيميشن الرسائل
      if (batch.messages.isNotEmpty) {
        _messageAnimationController.forward();
        _scrollToBottom(animated: false);
      }
    } catch (e) {
      setState(() {
        _loadingState = MessageLoadingState.error;
      });
    }
  }

  Future<void> _loadMoreMessages() async {
    if (_isLoadingMore ||
        !_hasMoreMessages ||
        _oldestMessageTimestamp == null) {
      return;
    }

    setState(() {
      _isLoadingMore = true;
    });

    try {
      final batch = await EnhancedChatService.loadMoreMessages(
        widget.chatRoom.id,
        _oldestMessageTimestamp!,
      );

      setState(() {
        _messages.addAll(batch.messages);
        _hasMoreMessages = batch.hasMore;
        _oldestMessageTimestamp =
            batch.messages.isNotEmpty
                ? batch.messages.last.timestamp
                : _oldestMessageTimestamp;
        _isLoadingMore = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingMore = false;
      });
    }
  }

  void _startListeningToMessages() {
    EnhancedChatService.getMessagesStream(widget.chatRoom.id).listen((
      messages,
    ) {
      setState(() {
        _messages = messages;
      });

      // تحديث عداد الرسائل في مدير الأداء
      ChatPerformanceManager().updateMessageCount(
        widget.chatRoom.id,
        messages.length,
      );

      // انيميشن للرسائل الجديدة
      if (messages.isNotEmpty && messages.first.isRecent) {
        _messageAnimationController.reset();
        _messageAnimationController.forward();
        _scrollToBottom();
      }
    });
  }

  void _scrollToBottom({bool animated = true}) {
    if (_scrollController.hasClients) {
      if (animated) {
        _scrollController.animateTo(
          0.0,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      } else {
        _scrollController.jumpTo(0.0);
      }
    }
  }

  Future<void> _sendMessage() async {
    final content = _messageController.text.trim();
    if (content.isEmpty) return;

    // تنظيف حقل الإدخال فوراً
    _messageController.clear();

    // إيقاف مؤشر الكتابة
    _setTyping(false);

    bool success;

    // إرسال الرسالة (عادية أو رد)
    if (_isReplying && _replyToMessage != null) {
      success = await EnhancedChatService.sendReply(
        chatRoomId: widget.chatRoom.id,
        content: content,
        replyToMessage: _replyToMessage!,
      );

      // إلغاء حالة الرد
      _cancelReply();
    } else {
      success = await EnhancedChatService.sendMessage(
        chatRoomId: widget.chatRoom.id,
        content: content,
      );
    }

    if (success) {
      // اهتزاز خفيف للتأكيد
      HapticFeedback.lightImpact();
      _scrollToBottom();
    } else {
      // إعادة النص في حالة الفشل
      _messageController.text = content;
      _showErrorSnackBar('فشل في إرسال الرسالة');
    }
  }

  /// بدء الرد على رسالة
  void _startReply(EnhancedChatMessage message) {
    setState(() {
      _replyToMessage = message;
      _isReplying = true;
    });

    // التركيز على حقل الإدخال
    _messageFocusNode.requestFocus();

    // اهتزاز خفيف
    HapticFeedback.selectionClick();
  }

  /// إلغاء الرد
  void _cancelReply() {
    setState(() {
      _replyToMessage = null;
      _isReplying = false;
    });
  }

  /// التنقل للرسالة المرجعية
  void _scrollToReplyMessage(String messageId) {
    // البحث عن الرسالة في القائمة
    final messageIndex = _messages.indexWhere((m) => m.id == messageId);

    if (messageIndex != -1) {
      // التمرير للرسالة
      final itemHeight = 80.0; // تقدير ارتفاع الرسالة
      final targetOffset = messageIndex * itemHeight;

      _scrollController.animateTo(
        targetOffset,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );

      // تأثير بصري (يمكن إضافة highlight للرسالة)
      HapticFeedback.lightImpact();
    }
  }

  void _setTyping(bool typing) {
    if (_isTyping != typing) {
      setState(() {
        _isTyping = typing;
      });
    }

    if (typing) {
      _typingTimer?.cancel();
      _typingTimer = Timer(const Duration(seconds: 2), () {
        _setTyping(false);
      });
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: GoogleFonts.cairo()),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<ThemeProvider, AuthProvider>(
      builder: (context, themeProvider, authProvider, child) {
        return SlideTransition(
          position: _slideAnimation,
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Scaffold(
              backgroundColor:
                  themeProvider.isDarkMode
                      ? const Color(0xFF0F172A)
                      : const Color(0xFFF8FAFC),
              appBar: _buildAppBar(themeProvider),
              body: Column(
                children: [
                  // محتوى الدردشة
                  Expanded(child: _buildChatContent(themeProvider)),

                  // معاينة الرد
                  if (_isReplying && _replyToMessage != null)
                    ReplyPreviewWidget(
                      replyToMessage: _replyToMessage!,
                      isDarkMode: themeProvider.isDarkMode,
                      onCancel: _cancelReply,
                    ),

                  // شريط إدخال الرسالة
                  _buildMessageInput(themeProvider),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  PreferredSizeWidget _buildAppBar(ThemeProvider themeProvider) {
    return AppBar(
      elevation: 0,
      backgroundColor: Colors.transparent,
      leading: IconButton(
        onPressed: () {
          // انيميشن الخروج
          _slideAnimationController.reverse().then((_) {
            Navigator.pop(context);
          });
        },
        icon: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Icon(
            Icons.arrow_back_ios_rounded,
            color: Colors.white,
            size: 20,
          ),
        ),
      ),
      flexibleSpace: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors:
                widget.chatRoom.isGeneral
                    ? [const Color(0xFF667EEA), const Color(0xFF764BA2)]
                    : _getAcademicYearGradient(widget.chatRoom.academicYear),
          ),
        ),
      ),
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.chatRoom.name,
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          Text(
            widget.chatRoom.description,
            style: GoogleFonts.cairo(
              fontSize: 12,
              color: Colors.white.withValues(alpha: 0.8),
            ),
          ),
        ],
      ),
      actions: [
        // زر اختبار السحب (في وضع التطوير فقط)
        if (kDebugMode)
          IconButton(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SwipeTestScreen(),
                ),
              );
            },
            icon: const Icon(Icons.bug_report, color: Colors.white),
            tooltip: 'اختبار السحب',
          ),
        IconButton(
          onPressed: () {
            // إعدادات الغرفة
          },
          icon: const Icon(Icons.more_vert_rounded, color: Colors.white),
        ),
      ],
    );
  }

  Widget _buildChatContent(ThemeProvider themeProvider) {
    switch (_loadingState) {
      case MessageLoadingState.loading:
        return _buildLoadingState();
      case MessageLoadingState.error:
        return _buildErrorState();
      case MessageLoadingState.empty:
        return _buildEmptyState();
      case MessageLoadingState.loaded:
        return _buildMessagesList(themeProvider);
      default:
        return _buildLoadingState();
    }
  }

  Widget _buildLoadingState() {
    return const Center(child: CircularProgressIndicator());
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          Text(
            'حدث خطأ في تحميل الرسائل',
            style: GoogleFonts.cairo(fontSize: 16),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadInitialMessages,
            child: Text('إعادة المحاولة', style: GoogleFonts.cairo()),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.chat_bubble_outline, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'لا توجد رسائل بعد',
            style: GoogleFonts.cairo(fontSize: 18, color: Colors.grey[600]),
          ),
          const SizedBox(height: 8),
          Text(
            'ابدأ المحادثة بإرسال أول رسالة',
            style: GoogleFonts.cairo(fontSize: 14, color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }

  Widget _buildMessagesList(ThemeProvider themeProvider) {
    return Column(
      children: [
        // مؤشر التحميل التدريجي
        PaginationLoadingWidget(
          isLoading: _isLoadingMore,
          hasMore: _hasMoreMessages,
          onLoadMore: _loadMoreMessages,
          loadingText: 'جاري تحميل رسائل أقدم...',
          noMoreText: 'وصلت لبداية المحادثة',
        ),

        // قائمة الرسائل
        Expanded(
          child: ListView.builder(
            controller: _scrollController,
            reverse: true, // الأحدث في الأسفل
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            itemCount: _messages.length,
            itemBuilder: (context, index) {
              final message = _messages[index];
              return _buildSwipeableMessage(message, themeProvider);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSwipeableMessage(
    EnhancedChatMessage message,
    ThemeProvider themeProvider,
  ) {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    // تحسين تحديد المستخدم الحالي
    final currentUserId =
        authProvider.userModel?.id ?? authProvider.firebaseUser?.uid;
    final isMe = message.senderId == currentUserId;

    if (kDebugMode) {
      print(
        '🔍 Message from: ${message.senderId}, Current user: $currentUserId, IsMe: $isMe',
      );
    }

    // استخدام المكون المبسط للاختبار في وضع التطوير
    if (kDebugMode) {
      return SimpleSwipeTest(
        isFromCurrentUser: isMe,
        onSwipe: () {
          if (kDebugMode) {
            print('🎉 تم تفعيل الرد للرسالة: ${message.content}');
          }
          _startReply(message);
        },
        child: _buildMessageBubble(message, themeProvider),
      );
    }

    return SwipeToReplyWidget(
      message: message,
      isFromCurrentUser: isMe,
      onReply: () => _startReply(message),
      replyIconColor: const Color(0xFF6366F1),
      child: _buildMessageBubble(message, themeProvider),
    );
  }

  Widget _buildMessageBubble(
    EnhancedChatMessage message,
    ThemeProvider themeProvider,
  ) {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final isMe = message.senderId == authProvider.userModel?.id;

    return AnimatedBuilder(
      animation: _messageAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: message.isRecent ? _messageAnimation.value : 1.0,
          child: Container(
            margin: const EdgeInsets.only(bottom: 8),
            child: Row(
              mainAxisAlignment:
                  isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
              children: [
                if (!isMe) ...[
                  CircleAvatar(
                    radius: 16,
                    backgroundColor: const Color(0xFF6366F1),
                    child: Text(
                      message.senderName.isNotEmpty
                          ? message.senderName[0].toUpperCase()
                          : 'م',
                      style: GoogleFonts.cairo(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                ],

                Flexible(
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                    decoration: BoxDecoration(
                      color:
                          isMe
                              ? const Color(0xFF6366F1)
                              : themeProvider.isDarkMode
                              ? const Color(0xFF1E293B)
                              : Colors.white,
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: MessageWithReplyWidget(
                      message: message,
                      isFromCurrentUser: isMe,
                      isDarkMode: themeProvider.isDarkMode,
                      onReplyTap: () {
                        if (message.replyToId != null) {
                          _scrollToReplyMessage(message.replyToId!);
                        }
                      },
                      messageContent: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (!isMe)
                            Text(
                              message.senderName,
                              style: GoogleFonts.cairo(
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                color: const Color(0xFF6366F1),
                              ),
                            ),

                          Text(
                            message.content,
                            style: GoogleFonts.cairo(
                              fontSize: 14,
                              color:
                                  isMe
                                      ? Colors.white
                                      : themeProvider.isDarkMode
                                      ? Colors.white
                                      : Colors.black87,
                            ),
                          ),

                          const SizedBox(height: 4),

                          Text(
                            message.formattedTime,
                            style: GoogleFonts.cairo(
                              fontSize: 10,
                              color:
                                  isMe
                                      ? Colors.white.withValues(alpha: 0.7)
                                      : Colors.grey[500],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

                if (isMe) ...[
                  const SizedBox(width: 8),
                  CircleAvatar(
                    radius: 16,
                    backgroundColor: const Color(0xFF10B981),
                    child: Text(
                      message.senderName.isNotEmpty
                          ? message.senderName[0].toUpperCase()
                          : 'أ',
                      style: GoogleFonts.cairo(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildMessageInput(ThemeProvider themeProvider) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color:
            themeProvider.isDarkMode ? const Color(0xFF1E293B) : Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color:
                    themeProvider.isDarkMode
                        ? const Color(0xFF334155)
                        : const Color(0xFFF1F5F9),
                borderRadius: BorderRadius.circular(25),
              ),
              child: TextField(
                controller: _messageController,
                focusNode: _messageFocusNode,
                style: GoogleFonts.cairo(
                  color:
                      themeProvider.isDarkMode ? Colors.white : Colors.black87,
                ),
                decoration: InputDecoration(
                  hintText: 'اكتب رسالة...',
                  hintStyle: GoogleFonts.cairo(color: Colors.grey[500]),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 12,
                  ),
                ),
                onChanged: (text) {
                  _setTyping(text.isNotEmpty);
                },
                onSubmitted: (_) => _sendMessage(),
              ),
            ),
          ),
          const SizedBox(width: 12),

          GestureDetector(
            onTap: _sendMessage,
            child: Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFF6366F1), Color(0xFF8B5CF6)],
                ),
                borderRadius: BorderRadius.circular(24),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF6366F1).withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: const Icon(
                Icons.send_rounded,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Color> _getAcademicYearGradient(String academicYear) {
    switch (academicYear) {
      case 'السنة الأولى':
        return [const Color(0xFF6366F1), const Color(0xFF8B5CF6)];
      case 'السنة الثانية':
        return [const Color(0xFFEC4899), const Color(0xFFF97316)];
      case 'السنة الثالثة':
        return [const Color(0xFF10B981), const Color(0xFF06B6D4)];
      case 'السنة الرابعة':
        return [const Color(0xFFF59E0B), const Color(0xFFEF4444)];
      default:
        return [const Color(0xFF6366F1), const Color(0xFF8B5CF6)];
    }
  }
}
