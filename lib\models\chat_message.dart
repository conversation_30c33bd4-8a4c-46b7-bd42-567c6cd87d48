import 'package:hive/hive.dart';

part 'chat_message.g.dart';

@HiveType(typeId: 0)
class ChatMessage extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String content;

  @HiveField(2)
  final String senderId;

  @HiveField(3)
  final String senderName;

  @HiveField(4)
  final DateTime timestamp;

  @HiveField(5)
  final String roomId;

  @HiveField(6)
  final bool isSent;

  @HiveField(7)
  final bool isRead;

  @HiveField(8)
  final String? replyToId;

  @HiveField(9)
  final String? replyToContent;

  @HiveField(10)
  final String? imageUrl;

  @HiveField(11)
  final String? fileUrl;

  @HiveField(12)
  final String? fileName;

  @HiveField(13)
  final MessageType type;

  @HiveField(14)
  final bool isEdited;

  @HiveField(15)
  final DateTime? editedAt;

  @HiveField(16)
  final List<String> reactions;

  @HiveField(17)
  final bool isDeleted;

  @HiveField(18)
  final String? localFilePath;

  @HiveField(19)
  final bool needsSync;

  ChatMessage({
    required this.id,
    required this.content,
    required this.senderId,
    required this.senderName,
    required this.timestamp,
    required this.roomId,
    this.isSent = false,
    this.isRead = false,
    this.replyToId,
    this.replyToContent,
    this.imageUrl,
    this.fileUrl,
    this.fileName,
    this.type = MessageType.text,
    this.isEdited = false,
    this.editedAt,
    this.reactions = const [],
    this.isDeleted = false,
    this.localFilePath,
    this.needsSync = false,
  });

  /// إنشاء نسخة محدثة من الرسالة
  ChatMessage copyWith({
    String? id,
    String? content,
    String? senderId,
    String? senderName,
    DateTime? timestamp,
    String? roomId,
    bool? isSent,
    bool? isRead,
    String? replyToId,
    String? replyToContent,
    String? imageUrl,
    String? fileUrl,
    String? fileName,
    MessageType? type,
    bool? isEdited,
    DateTime? editedAt,
    List<String>? reactions,
    bool? isDeleted,
    String? localFilePath,
    bool? needsSync,
  }) {
    return ChatMessage(
      id: id ?? this.id,
      content: content ?? this.content,
      senderId: senderId ?? this.senderId,
      senderName: senderName ?? this.senderName,
      timestamp: timestamp ?? this.timestamp,
      roomId: roomId ?? this.roomId,
      isSent: isSent ?? this.isSent,
      isRead: isRead ?? this.isRead,
      replyToId: replyToId ?? this.replyToId,
      replyToContent: replyToContent ?? this.replyToContent,
      imageUrl: imageUrl ?? this.imageUrl,
      fileUrl: fileUrl ?? this.fileUrl,
      fileName: fileName ?? this.fileName,
      type: type ?? this.type,
      isEdited: isEdited ?? this.isEdited,
      editedAt: editedAt ?? this.editedAt,
      reactions: reactions ?? this.reactions,
      isDeleted: isDeleted ?? this.isDeleted,
      localFilePath: localFilePath ?? this.localFilePath,
      needsSync: needsSync ?? this.needsSync,
    );
  }

  /// تحويل إلى Map للإرسال إلى Firebase
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'content': content,
      'senderId': senderId,
      'senderName': senderName,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'roomId': roomId,
      'isSent': isSent,
      'isRead': isRead,
      'replyToId': replyToId,
      'replyToContent': replyToContent,
      'imageUrl': imageUrl,
      'fileUrl': fileUrl,
      'fileName': fileName,
      'type': type.toString(),
      'isEdited': isEdited,
      'editedAt': editedAt?.millisecondsSinceEpoch,
      'reactions': reactions,
      'isDeleted': isDeleted,
    };
  }

  /// إنشاء من Map (من Firebase)
  factory ChatMessage.fromMap(Map<String, dynamic> map) {
    return ChatMessage(
      id: map['id'] ?? '',
      content: map['content'] ?? '',
      senderId: map['senderId'] ?? '',
      senderName: map['senderName'] ?? '',
      timestamp: DateTime.fromMillisecondsSinceEpoch(map['timestamp'] ?? 0),
      roomId: map['roomId'] ?? '',
      isSent: map['isSent'] ?? true,
      isRead: map['isRead'] ?? false,
      replyToId: map['replyToId'],
      replyToContent: map['replyToContent'],
      imageUrl: map['imageUrl'],
      fileUrl: map['fileUrl'],
      fileName: map['fileName'],
      type: MessageType.values.firstWhere(
        (e) => e.toString() == map['type'],
        orElse: () => MessageType.text,
      ),
      isEdited: map['isEdited'] ?? false,
      editedAt: map['editedAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['editedAt'])
          : null,
      reactions: List<String>.from(map['reactions'] ?? []),
      isDeleted: map['isDeleted'] ?? false,
      needsSync: false, // الرسائل من Firebase لا تحتاج مزامنة
    );
  }

  /// إنشاء رسالة محلية (للإرسال لاحقاً)
  factory ChatMessage.createLocal({
    required String content,
    required String senderId,
    required String senderName,
    required String roomId,
    String? replyToId,
    String? replyToContent,
    String? imageUrl,
    String? fileUrl,
    String? fileName,
    MessageType type = MessageType.text,
    String? localFilePath,
  }) {
    return ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: content,
      senderId: senderId,
      senderName: senderName,
      timestamp: DateTime.now(),
      roomId: roomId,
      isSent: false,
      isRead: false,
      replyToId: replyToId,
      replyToContent: replyToContent,
      imageUrl: imageUrl,
      fileUrl: fileUrl,
      fileName: fileName,
      type: type,
      localFilePath: localFilePath,
      needsSync: true,
    );
  }

  /// التحقق من وجود مرفقات
  bool get hasAttachment => imageUrl != null || fileUrl != null;

  /// التحقق من كونها رد
  bool get isReply => replyToId != null;

  /// الحصول على نص مختصر للمحتوى
  String get shortContent {
    if (content.length <= 50) return content;
    return '${content.substring(0, 50)}...';
  }

  /// التحقق من كون الرسالة حديثة (أقل من دقيقة)
  bool get isRecent {
    return DateTime.now().difference(timestamp).inMinutes < 1;
  }

  @override
  String toString() {
    return 'ChatMessage(id: $id, content: $shortContent, sender: $senderName, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChatMessage && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

@HiveType(typeId: 1)
enum MessageType {
  @HiveField(0)
  text,
  @HiveField(1)
  image,
  @HiveField(2)
  file,
  @HiveField(3)
  voice,
  @HiveField(4)
  video,
  @HiveField(5)
  location,
  @HiveField(6)
  system,
}

/// محول Hive للرسائل
class ChatMessageAdapter extends TypeAdapter<ChatMessage> {
  @override
  final int typeId = 0;

  @override
  ChatMessage read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ChatMessage(
      id: fields[0] as String,
      content: fields[1] as String,
      senderId: fields[2] as String,
      senderName: fields[3] as String,
      timestamp: fields[4] as DateTime,
      roomId: fields[5] as String,
      isSent: fields[6] as bool,
      isRead: fields[7] as bool,
      replyToId: fields[8] as String?,
      replyToContent: fields[9] as String?,
      imageUrl: fields[10] as String?,
      fileUrl: fields[11] as String?,
      fileName: fields[12] as String?,
      type: fields[13] as MessageType,
      isEdited: fields[14] as bool,
      editedAt: fields[15] as DateTime?,
      reactions: (fields[16] as List?)?.cast<String>() ?? [],
      isDeleted: fields[17] as bool,
      localFilePath: fields[18] as String?,
      needsSync: fields[19] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, ChatMessage obj) {
    writer
      ..writeByte(20)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.content)
      ..writeByte(2)
      ..write(obj.senderId)
      ..writeByte(3)
      ..write(obj.senderName)
      ..writeByte(4)
      ..write(obj.timestamp)
      ..writeByte(5)
      ..write(obj.roomId)
      ..writeByte(6)
      ..write(obj.isSent)
      ..writeByte(7)
      ..write(obj.isRead)
      ..writeByte(8)
      ..write(obj.replyToId)
      ..writeByte(9)
      ..write(obj.replyToContent)
      ..writeByte(10)
      ..write(obj.imageUrl)
      ..writeByte(11)
      ..write(obj.fileUrl)
      ..writeByte(12)
      ..write(obj.fileName)
      ..writeByte(13)
      ..write(obj.type)
      ..writeByte(14)
      ..write(obj.isEdited)
      ..writeByte(15)
      ..write(obj.editedAt)
      ..writeByte(16)
      ..write(obj.reactions)
      ..writeByte(17)
      ..write(obj.isDeleted)
      ..writeByte(18)
      ..write(obj.localFilePath)
      ..writeByte(19)
      ..write(obj.needsSync);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ChatMessageAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
