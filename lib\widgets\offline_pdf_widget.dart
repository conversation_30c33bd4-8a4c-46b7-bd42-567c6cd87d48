import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/offline_provider.dart';
import '../models/pdf_file.dart';
import '../widgets/offline_indicator.dart';

/// ويدجت عرض ملفات PDF للوضع غير المتصل
class OfflinePdfWidget extends StatefulWidget {
  final String? subject;
  final String? category;

  const OfflinePdfWidget({super.key, this.subject, this.category});

  @override
  State<OfflinePdfWidget> createState() => _OfflinePdfWidgetState();
}

class _OfflinePdfWidgetState extends State<OfflinePdfWidget> {
  List<PdfFile> _pdfs = [];
  List<PdfFile> _filteredPdfs = [];
  String _searchQuery = '';
  String _selectedCategory = 'all';
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadPdfs();
  }

  void _loadPdfs() {
    setState(() {
      _isLoading = true;
    });

    final offline = Provider.of<OfflineProvider>(context, listen: false);
    _pdfs = offline.getOfflinePdfs(
      subject: widget.subject,
      category: widget.category,
    );
    _filteredPdfs = _pdfs;

    setState(() {
      _isLoading = false;
    });
  }

  void _filterPdfs() {
    setState(() {
      _filteredPdfs =
          _pdfs.where((pdf) {
            final matchesSearch =
                _searchQuery.isEmpty ||
                pdf.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
                pdf.description?.toLowerCase().contains(
                      _searchQuery.toLowerCase(),
                    ) ==
                    true;

            final matchesCategory =
                _selectedCategory == 'all' || pdf.category == _selectedCategory;

            return matchesSearch && matchesCategory;
          }).toList();
    });
  }

  Future<void> _openPdf(PdfFile pdf) async {
    final offline = Provider.of<OfflineProvider>(context, listen: false);

    try {
      final filePath = await offline.openOfflinePdf(pdf.id);

      if (filePath != null) {
        // هنا يمكن فتح ملف PDF باستخدام مكتبة مثل flutter_pdfview
        // أو تمرير المسار إلى شاشة عرض PDF
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('فتح الملف: ${pdf.name}'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('الملف غير متاح للعرض'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في فتح الملف: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _downloadPdf(PdfFile pdf) async {
    final offline = Provider.of<OfflineProvider>(context, listen: false);

    if (!offline.isOnline) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يتطلب اتصال بالإنترنت للتحميل'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    try {
      final success = await offline.downloadPdfForOffline(pdf);

      if (success) {
        _loadPdfs();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم تحميل ${pdf.name} بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('فشل في تحميل الملف'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في التحميل: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _removePdf(PdfFile pdf) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('حذف الملف'),
            content: Text('هل تريد حذف "${pdf.name}" من التخزين المحلي؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                child: const Text('حذف'),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      final offline = Provider.of<OfflineProvider>(context, listen: false);
      final success = await offline.removeOfflinePdf(pdf.id);

      if (success) {
        _loadPdfs();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم حذف ${pdf.name}'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Consumer<OfflineProvider>(
      builder: (context, offline, child) {
        return OfflineIndicator(
          child: Scaffold(
            backgroundColor:
                isDark ? Colors.grey.shade900 : Colors.grey.shade50,
            appBar: AppBar(
              title: const Text('ملفات PDF المحفوظة'),
              backgroundColor: Colors.transparent,
              elevation: 0,
              actions: [
                ConnectionStatusDot(margin: const EdgeInsets.only(left: 16)),
              ],
            ),
            body: Column(
              children: [
                // شريط البحث والفلترة
                _buildSearchAndFilter(theme, isDark),

                // قائمة الملفات
                Expanded(
                  child:
                      _isLoading
                          ? const Center(child: CircularProgressIndicator())
                          : _filteredPdfs.isEmpty
                          ? _buildEmptyState(offline, isDark)
                          : _buildPdfList(offline, theme, isDark),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSearchAndFilter(ThemeData theme, bool isDark) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDark ? Colors.grey.shade800 : Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // شريط البحث
          TextField(
            decoration: InputDecoration(
              hintText: 'البحث في الملفات...',
              prefixIcon: const Icon(Icons.search_rounded),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: isDark ? Colors.grey.shade700 : Colors.grey.shade100,
            ),
            onChanged: (value) {
              _searchQuery = value;
              _filterPdfs();
            },
          ),

          const SizedBox(height: 12),

          // فلتر الفئات
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildCategoryChip('all', 'الكل', theme),
                _buildCategoryChip('tests', 'أسئلة', theme),
                _buildCategoryChip('exams', 'امتحانات', theme),
                _buildCategoryChip('summaries', 'ملخصات', theme),
                _buildCategoryChip('official_book', 'الكتاب الرسمي', theme),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryChip(String category, String label, ThemeData theme) {
    final isSelected = _selectedCategory == category;

    return Container(
      margin: const EdgeInsets.only(left: 8),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (selected) {
          setState(() {
            _selectedCategory = category;
          });
          _filterPdfs();
        },
        backgroundColor: Colors.grey.shade200,
        selectedColor: Colors.blue.shade100,
        checkmarkColor: Colors.blue.shade700,
      ),
    );
  }

  Widget _buildEmptyState(OfflineProvider offline, bool isDark) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.picture_as_pdf_rounded,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد ملفات محفوظة',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            offline.isOnline
                ? 'قم بتحميل ملفات PDF للوصول إليها بدون إنترنت'
                : 'الملفات المحفوظة ستظهر هنا',
            style: TextStyle(color: Colors.grey.shade500),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPdfList(OfflineProvider offline, ThemeData theme, bool isDark) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _filteredPdfs.length,
      itemBuilder: (context, index) {
        final pdf = _filteredPdfs[index];
        return _buildPdfCard(pdf, offline, theme, isDark);
      },
    );
  }

  Widget _buildPdfCard(
    PdfFile pdf,
    OfflineProvider offline,
    ThemeData theme,
    bool isDark,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: isDark ? Colors.grey.shade800 : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () => _openPdf(pdf),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // أيقونة PDF
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: Colors.red.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.picture_as_pdf_rounded,
                  color: Colors.red.shade600,
                  size: 24,
                ),
              ),

              const SizedBox(width: 16),

              // معلومات الملف
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      pdf.name,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: isDark ? Colors.white : Colors.grey.shade800,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.blue.shade100,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            pdf.categoryName,
                            style: TextStyle(
                              fontSize: 10,
                              color: Colors.blue.shade700,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          pdf.formattedFileSize,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade500,
                          ),
                        ),
                      ],
                    ),
                    if (pdf.description != null &&
                        pdf.description!.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        pdf.shortDescription,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ],
                ),
              ),

              // أزرار الإجراءات
              Column(
                children: [
                  if (pdf.isAvailableOffline)
                    IconButton(
                      onPressed: () => _removePdf(pdf),
                      icon: Icon(
                        Icons.delete_rounded,
                        color: Colors.red.shade400,
                        size: 20,
                      ),
                      tooltip: 'حذف من التخزين المحلي',
                    )
                  else
                    IconButton(
                      onPressed:
                          offline.isOnline ? () => _downloadPdf(pdf) : null,
                      icon: Icon(
                        Icons.download_rounded,
                        color:
                            offline.isOnline
                                ? Colors.blue.shade400
                                : Colors.grey.shade400,
                        size: 20,
                      ),
                      tooltip: 'تحميل للوصول غير المتصل',
                    ),

                  // مؤشر الحالة
                  Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color:
                          pdf.isAvailableOffline
                              ? Colors.green
                              : Colors.grey.shade400,
                      shape: BoxShape.circle,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
