import 'package:flutter/foundation.dart';
import 'services/enhanced_chat_service.dart';
import 'models/enhanced_chat_message.dart';

/// اختبار تشخيص مشكلة السحب للرد
class SwipeDebugTester {
  
  /// اختبار سريع لتشخيص مشكلة السحب
  static Future<void> debugSwipeIssue() async {
    if (!kDebugMode) {
      print('⚠️ اختبار التشخيص يعمل فقط في وضع التطوير');
      return;
    }

    print('🔍 بدء تشخيص مشكلة السحب للرد');
    print('=' * 50);

    try {
      // إنشاء رسائل تجريبية
      await _createTestMessages();
      
      // اختبار تحديد المستخدم
      await _testUserIdentification();
      
      // اختبار منطق السحب
      await _testSwipeLogic();
      
      print('=' * 50);
      print('✅ انتهى تشخيص مشكلة السحب');
      
    } catch (e) {
      print('❌ خطأ في التشخيص: $e');
    }
  }

  /// إنشاء رسائل تجريبية
  static Future<void> _createTestMessages() async {
    print('\n📝 إنشاء رسائل تجريبية...');
    
    try {
      final testRoomId = 'debug_room_${DateTime.now().millisecondsSinceEpoch}';
      
      // رسالة من المستخدم الحالي
      await EnhancedChatService.sendMessage(
        chatRoomId: testRoomId,
        content: 'رسالة من المستخدم الحالي',
      );
      
      await Future.delayed(const Duration(milliseconds: 100));
      
      // محاكاة رسالة من مستخدم آخر
      final otherUserMessage = EnhancedChatMessage(
        id: 'other_user_msg_${DateTime.now().millisecondsSinceEpoch}',
        senderId: 'other_user_id_123', // معرف مختلف
        senderName: 'مستخدم آخر',
        senderAvatar: '',
        content: 'رسالة من مستخدم آخر',
        timestamp: DateTime.now(),
      );
      
      print('✅ تم إنشاء الرسائل التجريبية');
      print('   • رسالة المستخدم الحالي: تم إرسالها');
      print('   • رسالة المستخدم الآخر: ${otherUserMessage.senderName}');
      
    } catch (e) {
      print('❌ خطأ في إنشاء الرسائل التجريبية: $e');
      rethrow;
    }
  }

  /// اختبار تحديد المستخدم
  static Future<void> _testUserIdentification() async {
    print('\n👤 اختبار تحديد المستخدم...');
    
    try {
      // محاكاة معرفات مختلفة
      final currentUserId = 'current_user_123';
      final otherUserId = 'other_user_456';
      
      // اختبار المقارنة
      final isCurrentUserMessage = currentUserId == currentUserId;
      final isOtherUserMessage = otherUserId == currentUserId;
      
      print('✅ نتائج تحديد المستخدم:');
      print('   • المستخدم الحالي ($currentUserId): $isCurrentUserMessage');
      print('   • المستخدم الآخر ($otherUserId): $isOtherUserMessage');
      
      if (isCurrentUserMessage && !isOtherUserMessage) {
        print('✅ تحديد المستخدم يعمل بشكل صحيح');
      } else {
        throw Exception('مشكلة في تحديد المستخدم');
      }
      
    } catch (e) {
      print('❌ خطأ في اختبار تحديد المستخدم: $e');
      rethrow;
    }
  }

  /// اختبار منطق السحب
  static Future<void> _testSwipeLogic() async {
    print('\n👆 اختبار منطق السحب...');
    
    try {
      // محاكاة حركات السحب
      final testCases = [
        {
          'description': 'سحب رسالة المستخدم الحالي لليسار',
          'isFromCurrentUser': true,
          'deltaX': -50.0,
          'expectedValid': true,
        },
        {
          'description': 'سحب رسالة المستخدم الحالي لليمين',
          'isFromCurrentUser': true,
          'deltaX': 50.0,
          'expectedValid': false,
        },
        {
          'description': 'سحب رسالة المستخدم الآخر لليمين',
          'isFromCurrentUser': false,
          'deltaX': 50.0,
          'expectedValid': true,
        },
        {
          'description': 'سحب رسالة المستخدم الآخر لليسار',
          'isFromCurrentUser': false,
          'deltaX': -50.0,
          'expectedValid': false,
        },
      ];

      for (final testCase in testCases) {
        final isFromCurrentUser = testCase['isFromCurrentUser'] as bool;
        final deltaX = testCase['deltaX'] as double;
        final expectedValid = testCase['expectedValid'] as bool;
        final description = testCase['description'] as String;

        // تطبيق منطق السحب
        final isValidDirection = isFromCurrentUser 
            ? deltaX < 0  // سحب لليسار للرسائل الخاصة بي
            : deltaX > 0; // سحب لليمين للرسائل الأخرى

        final result = isValidDirection == expectedValid ? '✅' : '❌';
        
        print('$result $description');
        print('   • isFromCurrentUser: $isFromCurrentUser');
        print('   • deltaX: $deltaX');
        print('   • isValidDirection: $isValidDirection');
        print('   • متوقع: $expectedValid');
        
        if (isValidDirection != expectedValid) {
          throw Exception('فشل في اختبار: $description');
        }
      }
      
      print('✅ جميع اختبارات منطق السحب نجحت');
      
    } catch (e) {
      print('❌ خطأ في اختبار منطق السحب: $e');
      rethrow;
    }
  }

  /// اختبار حساسية السحب
  static void testSwipeSensitivity() {
    print('\n📏 اختبار حساسية السحب...');
    
    final screenWidth = 400.0; // عرض شاشة افتراضي
    final swipeThreshold = 0.3; // 30%
    final requiredDistance = screenWidth * swipeThreshold; // 120 بكسل
    
    print('✅ إعدادات الحساسية:');
    print('   • عرض الشاشة: ${screenWidth}px');
    print('   • حد التفعيل: ${swipeThreshold * 100}%');
    print('   • المسافة المطلوبة: ${requiredDistance}px');
    
    // اختبار مسافات مختلفة
    final testDistances = [50.0, 100.0, 120.0, 150.0, 200.0];
    
    for (final distance in testDistances) {
      final progress = (distance / requiredDistance).clamp(0.0, 1.0);
      final isTriggered = progress >= 1.0;
      final result = isTriggered ? '🎉' : '⏳';
      
      print('$result مسافة ${distance}px: تقدم ${(progress * 100).toStringAsFixed(1)}% ${isTriggered ? "(مُفعل)" : ""}');
    }
  }

  /// طباعة نصائح الإصلاح
  static void printFixSuggestions() {
    print('\n💡 نصائح لإصلاح مشكلة السحب:');
    print('=' * 50);
    
    print('1. تحقق من تحديد المستخدم:');
    print('   • تأكد من أن authProvider.userModel?.id صحيح');
    print('   • تأكد من أن message.senderId صحيح');
    print('   • راجع logs التشخيص في الكونسول');
    
    print('\n2. تحقق من منطق السحب:');
    print('   • رسائل المستخدم الحالي: سحب لليسار (deltaX < 0)');
    print('   • رسائل المستخدمين الآخرين: سحب لليمين (deltaX > 0)');
    
    print('\n3. تحقق من حساسية السحب:');
    print('   • الحد الافتراضي: 30% من عرض الشاشة');
    print('   • يمكن تقليل الحد إلى 20% لسهولة أكبر');
    
    print('\n4. تحقق من الانيميشن:');
    print('   • تأكد من أن _slideController يتحرك');
    print('   • تأكد من أن _scaleController يتحرك');
    print('   • راجع قيم progress في الكونسول');
    
    print('\n5. اختبار بسيط:');
    print('   • جرب السحب ببطء وبقوة');
    print('   • راقب الكونسول للرسائل التشخيصية');
    print('   • تأكد من عدم وجود عوائق في الواجهة');
  }
}

/// دالة مساعدة لتشغيل التشخيص
Future<void> debugSwipeToReply() async {
  await SwipeDebugTester.debugSwipeIssue();
  SwipeDebugTester.testSwipeSensitivity();
  SwipeDebugTester.printFixSuggestions();
}
