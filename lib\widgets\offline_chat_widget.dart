import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/offline_provider.dart';
import '../models/chat_message.dart';
import '../widgets/offline_indicator.dart';

/// ويدجت الدردشة المحسن للوضع غير المتصل
class OfflineChatWidget extends StatefulWidget {
  final String roomId;
  final String roomName;
  final String currentUserId;
  final String currentUserName;

  const OfflineChatWidget({
    Key? key,
    required this.roomId,
    required this.roomName,
    required this.currentUserId,
    required this.currentUserName,
  }) : super(key: key);

  @override
  State<OfflineChatWidget> createState() => _OfflineChatWidgetState();
}

class _OfflineChatWidgetState extends State<OfflineChatWidget> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  List<ChatMessage> _messages = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadMessages();
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _loadMessages() {
    setState(() {
      _isLoading = true;
    });

    final offline = Provider.of<OfflineProvider>(context, listen: false);
    _messages = offline.getMessages(widget.roomId);

    setState(() {
      _isLoading = false;
    });

    // التمرير إلى أسفل
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  Future<void> _sendMessage() async {
    final content = _messageController.text.trim();
    if (content.isEmpty) return;

    final offline = Provider.of<OfflineProvider>(context, listen: false);

    try {
      await offline.sendMessage(
        content: content,
        senderId: widget.currentUserId,
        senderName: widget.currentUserName,
        roomId: widget.roomId,
      );

      _messageController.clear();
      _loadMessages();

      // إظهار رسالة حالة الإرسال
      if (!offline.isOnline && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Row(
              children: [
                Icon(Icons.schedule_rounded, color: Colors.white, size: 16),
                SizedBox(width: 8),
                Text('سيتم إرسال الرسالة عند عودة الاتصال'),
              ],
            ),
            backgroundColor: Colors.orange,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إرسال الرسالة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Consumer<OfflineProvider>(
      builder: (context, offline, child) {
        return OfflineIndicator(
          child: Scaffold(
            backgroundColor:
                isDark ? Colors.grey.shade900 : Colors.grey.shade50,
            appBar: AppBar(
              title: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(widget.roomName),
                  Text(
                    offline.getStatusMessage(),
                    style: TextStyle(
                      fontSize: 12,
                      color: theme.appBarTheme.foregroundColor?.withValues(
                        alpha: 0.7,
                      ),
                    ),
                  ),
                ],
              ),
              backgroundColor: Colors.transparent,
              elevation: 0,
              actions: [
                ConnectionStatusDot(margin: const EdgeInsets.only(left: 16)),
                if (offline.shouldShowSyncIndicator())
                  Container(
                    margin: const EdgeInsets.only(left: 8),
                    child: IconButton(
                      icon:
                          offline.isSyncing
                              ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                ),
                              )
                              : const Icon(Icons.sync_rounded),
                      onPressed:
                          offline.isSyncing
                              ? null
                              : () async {
                                final result = await offline.forceSync();
                                if (mounted) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text(result.message),
                                      backgroundColor:
                                          result.success
                                              ? Colors.green
                                              : Colors.red,
                                    ),
                                  );
                                }
                              },
                    ),
                  ),
              ],
            ),
            body: Column(
              children: [
                // قائمة الرسائل
                Expanded(
                  child:
                      _isLoading
                          ? const Center(child: CircularProgressIndicator())
                          : _messages.isEmpty
                          ? _buildEmptyState(offline, isDark)
                          : ListView.builder(
                            controller: _scrollController,
                            padding: const EdgeInsets.all(16),
                            itemCount: _messages.length,
                            itemBuilder: (context, index) {
                              return _buildMessageBubble(
                                _messages[index],
                                offline,
                                isDark,
                              );
                            },
                          ),
                ),

                // شريط الكتابة
                _buildMessageInput(offline, theme, isDark),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildEmptyState(OfflineProvider offline, bool isDark) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            offline.isOnline
                ? Icons.chat_bubble_outline_rounded
                : Icons.chat_bubble_outline_rounded,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            offline.isOnline ? 'لا توجد رسائل بعد' : 'لا توجد رسائل محفوظة',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            offline.isOnline
                ? 'ابدأ المحادثة بإرسال رسالة'
                : 'الرسائل المحفوظة ستظهر هنا',
            style: TextStyle(color: Colors.grey.shade500),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageBubble(
    ChatMessage message,
    OfflineProvider offline,
    bool isDark,
  ) {
    final isMe = message.senderId == widget.currentUserId;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Row(
        mainAxisAlignment:
            isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          if (!isMe) ...[
            CircleAvatar(
              radius: 16,
              backgroundColor: Colors.blue.shade100,
              child: Text(
                message.senderName.isNotEmpty
                    ? message.senderName[0].toUpperCase()
                    : '؟',
                style: TextStyle(
                  color: Colors.blue.shade700,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(width: 8),
          ],
          Flexible(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color:
                    isMe
                        ? Colors.blue.shade500
                        : isDark
                        ? Colors.grey.shade700
                        : Colors.white,
                borderRadius: BorderRadius.circular(20).copyWith(
                  bottomLeft:
                      isMe
                          ? const Radius.circular(20)
                          : const Radius.circular(4),
                  bottomRight:
                      isMe
                          ? const Radius.circular(4)
                          : const Radius.circular(20),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (!isMe)
                    Text(
                      message.senderName,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: Colors.blue.shade600,
                      ),
                    ),
                  if (!isMe) const SizedBox(height: 4),
                  Text(
                    message.content,
                    style: TextStyle(
                      color:
                          isMe
                              ? Colors.white
                              : (isDark ? Colors.white : Colors.grey.shade800),
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        _formatTime(message.timestamp),
                        style: TextStyle(
                          fontSize: 10,
                          color:
                              isMe
                                  ? Colors.white.withValues(alpha: 0.7)
                                  : Colors.grey.shade500,
                        ),
                      ),
                      if (isMe) ...[
                        const SizedBox(width: 4),
                        Icon(
                          message.isSent
                              ? Icons.done_all_rounded
                              : Icons.schedule_rounded,
                          size: 12,
                          color:
                              message.isSent
                                  ? Colors.white.withValues(alpha: 0.7)
                                  : Colors.orange.shade300,
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
          ),
          if (isMe) ...[
            const SizedBox(width: 8),
            CircleAvatar(
              radius: 16,
              backgroundColor: Colors.green.shade100,
              child: Text(
                message.senderName.isNotEmpty
                    ? message.senderName[0].toUpperCase()
                    : '؟',
                style: TextStyle(
                  color: Colors.green.shade700,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildMessageInput(
    OfflineProvider offline,
    ThemeData theme,
    bool isDark,
  ) {
    final canSend = offline.canSendMessages();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDark ? Colors.grey.shade800 : Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: isDark ? Colors.grey.shade700 : Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(24),
                ),
                child: TextField(
                  controller: _messageController,
                  enabled: canSend,
                  decoration: InputDecoration(
                    hintText: canSend ? 'اكتب رسالة...' : 'الكتابة غير متاحة',
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 12,
                    ),
                  ),
                  maxLines: null,
                  textInputAction: TextInputAction.send,
                  onSubmitted: canSend ? (_) => _sendMessage() : null,
                ),
              ),
            ),
            const SizedBox(width: 8),
            Container(
              decoration: BoxDecoration(
                color: canSend ? Colors.blue.shade500 : Colors.grey.shade400,
                shape: BoxShape.circle,
              ),
              child: IconButton(
                onPressed: canSend ? _sendMessage : null,
                icon: const Icon(Icons.send_rounded, color: Colors.white),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${dateTime.day}/${dateTime.month}';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}س';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}د';
    } else {
      return 'الآن';
    }
  }
}
