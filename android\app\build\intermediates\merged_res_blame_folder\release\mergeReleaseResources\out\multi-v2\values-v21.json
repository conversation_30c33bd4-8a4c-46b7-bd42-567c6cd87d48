{"logs": [{"outputFile": "com.legal2025.yamy.app-mergeReleaseResources-67:/values-v21/values-v21.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e53585da63edf0e2fa4a6374876313c7\\transformed\\appcompat-1.1.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,17,19,20,21,22,24,26,27,28,29,30,32,34,36,38,40,42,43,48,50,52,53,54,56,58,59,60,61,62,63,106,109,152,155,158,160,162,164,167,171,174,175,176,179,180,181,182,183,184,187,188,190,192,194,196,200,202,203,204,205,207,211,213,215,216,217,218,219,220,222,223,224,234,235,236,248", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,146,249,352,457,564,673,782,891,1000,1109,1216,1319,1438,1593,1748,1853,1974,2075,2222,2363,2466,2585,2692,2795,2950,3121,3270,3435,3592,3743,3862,4213,4362,4511,4623,4770,4923,5070,5145,5234,5321,5422,5525,8499,8684,11670,11867,12066,12189,12312,12425,12608,12863,13064,13153,13264,13497,13598,13693,13816,13945,14062,14239,14338,14473,14616,14751,14870,15071,15190,15283,15394,15450,15557,15752,15863,15996,16091,16182,16273,16366,16483,16622,16693,16776,17456,17513,17571,18265", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,16,18,19,20,21,23,25,26,27,28,29,31,33,35,37,39,41,42,47,49,51,52,53,55,57,58,59,60,61,62,105,108,151,154,157,159,161,163,166,170,173,174,175,178,179,180,181,182,183,186,187,189,191,193,195,199,201,202,203,204,206,210,212,214,215,216,217,218,219,221,222,223,233,234,235,247,259", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "141,244,347,452,559,668,777,886,995,1104,1211,1314,1433,1588,1743,1848,1969,2070,2217,2358,2461,2580,2687,2790,2945,3116,3265,3430,3587,3738,3857,4208,4357,4506,4618,4765,4918,5065,5140,5229,5316,5417,5520,8494,8679,11665,11862,12061,12184,12307,12420,12603,12858,13059,13148,13259,13492,13593,13688,13811,13940,14057,14234,14333,14468,14611,14746,14865,15066,15185,15278,15389,15445,15552,15747,15858,15991,16086,16177,16268,16361,16478,16617,16688,16771,17451,17508,17566,18260,18966"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,22,24,25,26,27,29,31,32,33,34,35,37,39,41,43,45,47,48,53,55,57,58,59,61,63,64,65,66,67,68,111,114,157,160,163,165,167,169,172,176,179,180,181,184,185,186,187,188,189,192,193,195,197,199,201,205,207,208,209,210,212,216,218,220,221,222,223,224,225,227,228,229,239,240,241,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "433,524,627,730,835,942,1051,1160,1269,1378,1487,1594,1697,1816,1971,2126,2231,2352,2453,2600,2741,2844,2963,3070,3173,3328,3499,3648,3813,3970,4121,4240,4591,4740,4889,5001,5148,5301,5448,5523,5612,5699,5800,5903,8661,8846,11616,11813,12012,12135,12258,12371,12554,12809,13010,13099,13210,13443,13544,13639,13762,13891,14008,14185,14284,14419,14562,14697,14816,15017,15136,15229,15340,15396,15503,15698,15809,15942,16037,16128,16219,16312,16429,16568,16639,16722,17345,17402,17460,18084", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,21,23,24,25,26,28,30,31,32,33,34,36,38,40,42,44,46,47,52,54,56,57,58,60,62,63,64,65,66,67,110,113,156,159,162,164,166,168,171,175,178,179,180,183,184,185,186,187,188,191,192,194,196,198,200,204,206,207,208,209,211,215,217,219,220,221,222,223,224,226,227,228,238,239,240,252,264", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "519,622,725,830,937,1046,1155,1264,1373,1482,1589,1692,1811,1966,2121,2226,2347,2448,2595,2736,2839,2958,3065,3168,3323,3494,3643,3808,3965,4116,4235,4586,4735,4884,4996,5143,5296,5443,5518,5607,5694,5795,5898,8656,8841,11611,11808,12007,12130,12253,12366,12549,12804,13005,13094,13205,13438,13539,13634,13757,13886,14003,14180,14279,14414,14557,14692,14811,15012,15131,15224,15335,15391,15498,15693,15804,15937,16032,16123,16214,16307,16424,16563,16634,16717,17340,17397,17455,18079,18715"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13f2593a09a7e676e6a4e2c9d70ef7ac\\transformed\\core-1.13.1\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,13", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,237,304,368,484,610,736,864,1036", "endLines": "2,3,4,5,6,7,8,9,12,17", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,232,299,363,479,605,731,859,1031,1383"}, "to": {"startLines": "2,3,4,5,271,272,279,283,295,298", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,237,304,19054,19170,19627,19921,20709,20881", "endLines": "2,3,4,5,271,272,279,283,297,302", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,232,299,363,19165,19291,19748,20044,20876,21228"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1d0a3f573332b3be097db894daafbd8d\\transformed\\media-1.1.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,5,8,11", "startColumns": "4,4,4,4", "startOffsets": "55,223,386,554", "endLines": "4,7,10,13", "endColumns": "12,12,12,12", "endOffsets": "218,381,549,716"}, "to": {"startLines": "273,276,280,284", "startColumns": "4,4,4,4", "startOffsets": "19296,19464,19753,20049", "endLines": "275,278,282,286", "endColumns": "12,12,12,12", "endOffsets": "19459,19622,19916,20211"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b5a70d0ba7abaf74188a334ea68a3f12\\transformed\\jetified-credentials-play-services-auth-1.2.0-rc01\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "9", "endColumns": "12", "endOffsets": "543"}, "to": {"startLines": "287", "startColumns": "4", "startOffsets": "20216", "endLines": "294", "endColumns": "12", "endOffsets": "20704"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\76de88675158c32d011d90413b6fc543\\transformed\\preference-1.2.1\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,6", "startColumns": "4,4,4", "startOffsets": "55,120,276", "endLines": "2,5,8", "endColumns": "64,12,12", "endOffsets": "115,271,449"}, "to": {"startLines": "6,265,268", "startColumns": "4,4,4", "startOffsets": "368,18720,18876", "endLines": "6,267,270", "endColumns": "64,12,12", "endOffsets": "428,18871,19049"}}]}]}