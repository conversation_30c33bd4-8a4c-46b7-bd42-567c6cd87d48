{"logs": [{"outputFile": "com.legal2025.yamy.app-mergeReleaseResources-67:/values-th/values-th.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13f2593a09a7e676e6a4e2c9d70ef7ac\\transformed\\core-1.13.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,352,450,553,658,770", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "146,249,347,445,548,653,765,866"}, "to": {"startLines": "31,32,33,34,35,36,37,65", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2960,3056,3159,3257,3355,3458,3563,6666", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "3051,3154,3252,3350,3453,3558,3670,6762"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\305e40512bf13f0ccd5d12e0c8791b8d\\transformed\\jetified-play-services-base-18.1.0\\res\\values-th\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,438,557,660,792,912,1027,1131,1271,1372,1515,1633,1769,1916,1976,2040", "endColumns": "101,142,118,102,131,119,114,103,139,100,142,117,135,146,59,63,79", "endOffsets": "294,437,556,659,791,911,1026,1130,1270,1371,1514,1632,1768,1915,1975,2039,2119"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3675,3781,3928,4051,4158,4294,4418,4537,4774,4918,5023,5170,5292,5432,5583,5647,5715", "endColumns": "105,146,122,106,135,123,118,107,143,104,146,121,139,150,63,67,83", "endOffsets": "3776,3923,4046,4153,4289,4413,4532,4640,4913,5018,5165,5287,5427,5578,5642,5710,5794"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5813dff2fc04f128ab917c6cfbab969f\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-th\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "124", "endOffsets": "319"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4645", "endColumns": "128", "endOffsets": "4769"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7be1c0bb4075e2006ba23d0329c62fdb\\transformed\\browser-1.8.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,257,368", "endColumns": "102,98,110,97", "endOffsets": "153,252,363,461"}, "to": {"startLines": "57,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "5869,6058,6157,6268", "endColumns": "102,98,110,97", "endOffsets": "5967,6152,6263,6361"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1214412e3b6f657b0a3026c46d581630\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,113", "endOffsets": "162,276"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2734,2846", "endColumns": "111,113", "endOffsets": "2841,2955"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\76de88675158c32d011d90413b6fc543\\transformed\\preference-1.2.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,261,341,480,648,728", "endColumns": "69,85,79,138,167,79,77", "endOffsets": "170,256,336,475,643,723,801"}, "to": {"startLines": "56,58,62,63,66,67,68", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5799,5972,6366,6446,6767,6935,7015", "endColumns": "69,85,79,138,167,79,77", "endOffsets": "5864,6053,6441,6580,6930,7010,7088"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e53585da63edf0e2fa4a6374876313c7\\transformed\\appcompat-1.1.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,303,411,496,598,708,786,864,955,1047,1138,1232,1333,1426,1521,1615,1706,1797,1877,1980,2078,2176,2279,2385,2486,2639,2734", "endColumns": "104,92,107,84,101,109,77,77,90,91,90,93,100,92,94,93,90,90,79,102,97,97,102,105,100,152,94,80", "endOffsets": "205,298,406,491,593,703,781,859,950,1042,1133,1227,1328,1421,1516,1610,1701,1792,1872,1975,2073,2171,2274,2380,2481,2634,2729,2810"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,303,411,496,598,708,786,864,955,1047,1138,1232,1333,1426,1521,1615,1706,1797,1877,1980,2078,2176,2279,2385,2486,2639,6585", "endColumns": "104,92,107,84,101,109,77,77,90,91,90,93,100,92,94,93,90,90,79,102,97,97,102,105,100,152,94,80", "endOffsets": "205,298,406,491,593,703,781,859,950,1042,1133,1227,1328,1421,1516,1610,1701,1792,1872,1975,2073,2171,2274,2380,2481,2634,2729,6661"}}]}]}