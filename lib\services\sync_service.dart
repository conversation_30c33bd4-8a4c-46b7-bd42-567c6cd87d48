import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/chat_message.dart';
import '../models/pdf_file.dart';
import '../models/user_data.dart';
import 'local_storage_service.dart';
import 'connectivity_service.dart';

/// خدمة مزامنة البيانات بين التخزين المحلي و Firebase
class SyncService {
  static final SyncService _instance = SyncService._internal();
  factory SyncService() => _instance;
  SyncService._internal();

  final LocalStorageService _localStorage = LocalStorageService();
  final ConnectivityService _connectivity = ConnectivityService();
  final FirebaseDatabase _database = FirebaseDatabase.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  bool _isSyncing = false;
  Timer? _syncTimer;
  StreamSubscription<bool>? _connectivitySubscription;

  /// تهيئة خدمة المزامنة
  Future<void> initialize() async {
    try {
      // مراقبة حالة الاتصال
      _connectivitySubscription = _connectivity.connectionStream.listen((isConnected) {
        if (isConnected && !_isSyncing) {
          _performAutoSync();
        }
      });

      // مزامنة دورية كل 5 دقائق عند وجود اتصال
      _syncTimer = Timer.periodic(const Duration(minutes: 5), (timer) {
        if (_connectivity.isConnected && !_isSyncing) {
          _performAutoSync();
        }
      });

      debugPrint('✅ تم تهيئة خدمة المزامنة');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة المزامنة: $e');
    }
  }

  /// مزامنة تلقائية
  Future<void> _performAutoSync() async {
    try {
      await syncAll();
    } catch (e) {
      debugPrint('❌ خطأ في المزامنة التلقائية: $e');
    }
  }

  /// مزامنة شاملة لجميع البيانات
  Future<SyncResult> syncAll() async {
    if (_isSyncing) {
      return SyncResult(
        success: false,
        message: 'المزامنة قيد التنفيذ بالفعل',
      );
    }

    if (!_connectivity.isConnected) {
      return SyncResult(
        success: false,
        message: 'لا يوجد اتصال بالإنترنت',
      );
    }

    final user = _auth.currentUser;
    if (user == null) {
      return SyncResult(
        success: false,
        message: 'المستخدم غير مسجل الدخول',
      );
    }

    _isSyncing = true;
    debugPrint('🔄 بدء المزامنة الشاملة...');

    try {
      final results = await Future.wait([
        _syncMessages(),
        _syncUserData(),
        _syncPdfFiles(),
      ]);

      final totalSynced = results.fold<int>(0, (sum, result) => sum + result.itemsSynced);
      final hasErrors = results.any((result) => !result.success);

      debugPrint('✅ انتهت المزامنة: $totalSynced عنصر تم مزامنته');

      return SyncResult(
        success: !hasErrors,
        message: hasErrors 
            ? 'تمت المزامنة مع بعض الأخطاء'
            : 'تمت المزامنة بنجاح',
        itemsSynced: totalSynced,
      );
    } catch (e) {
      debugPrint('❌ خطأ في المزامنة الشاملة: $e');
      return SyncResult(
        success: false,
        message: 'فشلت المزامنة: $e',
      );
    } finally {
      _isSyncing = false;
    }
  }

  /// مزامنة الرسائل
  Future<SyncResult> _syncMessages() async {
    try {
      debugPrint('📨 مزامنة الرسائل...');
      
      // رفع الرسائل غير المرسلة
      final unsentMessages = _localStorage.getUnsentMessages();
      int uploadedCount = 0;

      for (final message in unsentMessages) {
        try {
          await _uploadMessage(message);
          await _localStorage.markMessageAsSent(message.id);
          uploadedCount++;
        } catch (e) {
          debugPrint('❌ فشل رفع الرسالة ${message.id}: $e');
        }
      }

      // تحميل الرسائل الجديدة من Firebase
      int downloadedCount = 0;
      final rooms = await _getUserChatRooms();
      
      for (final roomId in rooms) {
        try {
          final newMessages = await _downloadNewMessages(roomId);
          if (newMessages.isNotEmpty) {
            await _localStorage.saveMessages(newMessages);
            downloadedCount += newMessages.length;
          }
        } catch (e) {
          debugPrint('❌ فشل تحميل رسائل الغرفة $roomId: $e');
        }
      }

      debugPrint('📨 تم رفع $uploadedCount رسالة وتحميل $downloadedCount رسالة');

      return SyncResult(
        success: true,
        message: 'تمت مزامنة الرسائل',
        itemsSynced: uploadedCount + downloadedCount,
      );
    } catch (e) {
      debugPrint('❌ خطأ في مزامنة الرسائل: $e');
      return SyncResult(
        success: false,
        message: 'فشلت مزامنة الرسائل: $e',
      );
    }
  }

  /// رفع رسالة إلى Firebase
  Future<void> _uploadMessage(ChatMessage message) async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('المستخدم غير مسجل الدخول');

    final messageRef = _database
        .ref('chats/${message.roomId}/messages')
        .child(message.id);

    await messageRef.set(message.toMap());
  }

  /// تحميل الرسائل الجديدة من Firebase
  Future<List<ChatMessage>> _downloadNewMessages(String roomId) async {
    try {
      // الحصول على آخر رسالة محلية
      final localMessages = _localStorage.getLatestMessagesForRoom(roomId, limit: 1);
      final lastTimestamp = localMessages.isNotEmpty 
          ? localMessages.first.timestamp.millisecondsSinceEpoch
          : 0;

      // تحميل الرسائل الأحدث من Firebase
      final messagesRef = _database
          .ref('chats/$roomId/messages')
          .orderByChild('timestamp')
          .startAt(lastTimestamp + 1);

      final snapshot = await messagesRef.get();
      
      if (!snapshot.exists) return [];

      final messages = <ChatMessage>[];
      for (final child in snapshot.children) {
        try {
          final data = Map<String, dynamic>.from(child.value as Map);
          messages.add(ChatMessage.fromMap(data));
        } catch (e) {
          debugPrint('❌ خطأ في تحليل الرسالة: $e');
        }
      }

      return messages;
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الرسائل الجديدة: $e');
      return [];
    }
  }

  /// الحصول على قائمة غرف الدردشة للمستخدم
  Future<List<String>> _getUserChatRooms() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return [];

      // يمكن تخصيص هذا حسب هيكل البيانات
      return ['general', 'first_year', 'second_year', 'third_year', 'fourth_year'];
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على غرف الدردشة: $e');
      return [];
    }
  }

  /// مزامنة بيانات المستخدم
  Future<SyncResult> _syncUserData() async {
    try {
      debugPrint('👤 مزامنة بيانات المستخدم...');
      
      final user = _auth.currentUser;
      if (user == null) {
        return SyncResult(
          success: false,
          message: 'المستخدم غير مسجل الدخول',
        );
      }

      // تحميل بيانات المستخدم من Firebase
      final userRef = _database.ref('users/${user.uid}');
      final snapshot = await userRef.get();

      if (snapshot.exists) {
        final data = Map<String, dynamic>.from(snapshot.value as Map);
        final userData = UserData.fromMap(data);
        await _localStorage.saveUserData(userData);
        
        debugPrint('👤 تم تحديث بيانات المستخدم');
        return SyncResult(
          success: true,
          message: 'تمت مزامنة بيانات المستخدم',
          itemsSynced: 1,
        );
      }

      return SyncResult(
        success: true,
        message: 'لا توجد بيانات مستخدم للمزامنة',
      );
    } catch (e) {
      debugPrint('❌ خطأ في مزامنة بيانات المستخدم: $e');
      return SyncResult(
        success: false,
        message: 'فشلت مزامنة بيانات المستخدم: $e',
      );
    }
  }

  /// مزامنة ملفات PDF
  Future<SyncResult> _syncPdfFiles() async {
    try {
      debugPrint('📄 مزامنة ملفات PDF...');
      
      // تحميل قائمة ملفات PDF من Firebase
      final pdfRef = _database.ref('pdfs');
      final snapshot = await pdfRef.get();

      if (!snapshot.exists) {
        return SyncResult(
          success: true,
          message: 'لا توجد ملفات PDF للمزامنة',
        );
      }

      int syncedCount = 0;
      final localPdfs = _localStorage.getAllPdfFiles();
      final localPdfIds = localPdfs.map((pdf) => pdf.id).toSet();

      for (final child in snapshot.children) {
        try {
          final data = Map<String, dynamic>.from(child.value as Map);
          final pdfFile = PdfFile.fromMap(data);
          
          // حفظ فقط الملفات الجديدة أو المحدثة
          if (!localPdfIds.contains(pdfFile.id)) {
            await _localStorage.savePdfFile(pdfFile);
            syncedCount++;
          }
        } catch (e) {
          debugPrint('❌ خطأ في تحليل ملف PDF: $e');
        }
      }

      debugPrint('📄 تم مزامنة $syncedCount ملف PDF');

      return SyncResult(
        success: true,
        message: 'تمت مزامنة ملفات PDF',
        itemsSynced: syncedCount,
      );
    } catch (e) {
      debugPrint('❌ خطأ في مزامنة ملفات PDF: $e');
      return SyncResult(
        success: false,
        message: 'فشلت مزامنة ملفات PDF: $e',
      );
    }
  }

  /// مزامنة رسالة واحدة فوراً
  Future<bool> syncMessage(ChatMessage message) async {
    if (!_connectivity.isConnected) {
      // حفظ محلياً للمزامنة لاحقاً
      await _localStorage.saveMessage(message);
      return false;
    }

    try {
      await _uploadMessage(message);
      await _localStorage.markMessageAsSent(message.id);
      return true;
    } catch (e) {
      debugPrint('❌ فشل مزامنة الرسالة: $e');
      // حفظ محلياً للمزامنة لاحقاً
      await _localStorage.saveMessage(message);
      return false;
    }
  }

  /// فرض المزامنة الفورية
  Future<SyncResult> forcSync() async {
    if (_isSyncing) {
      return SyncResult(
        success: false,
        message: 'المزامنة قيد التنفيذ بالفعل',
      );
    }

    return await syncAll();
  }

  /// الحصول على حالة المزامنة
  SyncStatus getSyncStatus() {
    final unsentMessages = _localStorage.getUnsentMessages();
    
    return SyncStatus(
      isSyncing: _isSyncing,
      isConnected: _connectivity.isConnected,
      pendingMessages: unsentMessages.length,
      lastSyncTime: _localStorage.getUserData()?.lastSyncAt,
    );
  }

  /// تنظيف الموارد
  void dispose() {
    _syncTimer?.cancel();
    _connectivitySubscription?.cancel();
    debugPrint('🔒 تم إغلاق خدمة المزامنة');
  }
}

/// نتيجة عملية المزامنة
class SyncResult {
  final bool success;
  final String message;
  final int itemsSynced;
  final DateTime timestamp;

  SyncResult({
    required this.success,
    required this.message,
    this.itemsSynced = 0,
  }) : timestamp = DateTime.now();

  @override
  String toString() {
    return 'SyncResult(success: $success, message: $message, items: $itemsSynced)';
  }
}

/// حالة المزامنة
class SyncStatus {
  final bool isSyncing;
  final bool isConnected;
  final int pendingMessages;
  final DateTime? lastSyncTime;

  SyncStatus({
    required this.isSyncing,
    required this.isConnected,
    required this.pendingMessages,
    this.lastSyncTime,
  });

  /// التحقق من الحاجة للمزامنة
  bool get needsSync => pendingMessages > 0 || _isStale;

  /// التحقق من كون آخر مزامنة قديمة (أكثر من ساعة)
  bool get _isStale {
    if (lastSyncTime == null) return true;
    return DateTime.now().difference(lastSyncTime!).inHours > 1;
  }

  /// الحصول على وصف الحالة
  String get statusDescription {
    if (isSyncing) return 'جاري المزامنة...';
    if (!isConnected) return 'لا يوجد اتصال';
    if (pendingMessages > 0) return '$pendingMessages رسالة في الانتظار';
    if (lastSyncTime == null) return 'لم تتم المزامنة بعد';
    
    final timeDiff = DateTime.now().difference(lastSyncTime!);
    if (timeDiff.inMinutes < 1) return 'تمت المزامنة للتو';
    if (timeDiff.inHours < 1) return 'تمت المزامنة منذ ${timeDiff.inMinutes} دقيقة';
    if (timeDiff.inDays < 1) return 'تمت المزامنة منذ ${timeDiff.inHours} ساعة';
    return 'تمت المزامنة منذ ${timeDiff.inDays} يوم';
  }

  @override
  String toString() {
    return 'SyncStatus(syncing: $isSyncing, connected: $isConnected, pending: $pendingMessages)';
  }
}
