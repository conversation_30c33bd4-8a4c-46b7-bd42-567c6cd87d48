import 'package:flutter/foundation.dart';
import '../models/enhanced_chat_message.dart' as enhanced;
import '../models/chat_model.dart' as legacy;
import '../services/enhanced_chat_service.dart';
import '../services/chat_service.dart';
import 'chat_performance_manager.dart';

/// مدير التكامل بين نظام الدردشة القديم والجديد
class ChatSystemIntegration {
  static final ChatSystemIntegration _instance =
      ChatSystemIntegration._internal();
  factory ChatSystemIntegration() => _instance;
  ChatSystemIntegration._internal();

  bool _isEnhancedModeEnabled = true;
  bool _isInitialized = false;

  /// تهيئة نظام التكامل
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // تهيئة النظام المحسن
      await EnhancedChatService.initialize();

      // تهيئة مدير الأداء
      ChatPerformanceManager().initialize();

      _isInitialized = true;

      if (kDebugMode) {
        debugPrint('✅ تم تهيئة نظام التكامل للدردشة');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في تهيئة نظام التكامل: $e');
      }
      // العودة للنظام القديم في حالة الخطأ
      _isEnhancedModeEnabled = false;
    }
  }

  /// التحقق من تمكين النظام المحسن
  bool get isEnhancedModeEnabled => _isEnhancedModeEnabled && _isInitialized;

  /// تحويل رسالة من النظام القديم للجديد
  static enhanced.EnhancedChatMessage convertLegacyMessage(
    legacy.MessageModel legacyMessage,
  ) {
    return enhanced.EnhancedChatMessage(
      id: legacyMessage.id,
      senderId: legacyMessage.senderId,
      senderName: legacyMessage.senderName,
      senderAvatar: '', // النظام القديم لا يحتوي على avatar
      content: legacyMessage.message,
      timestamp: legacyMessage.timestamp ?? DateTime.now(),
      type: _convertMessageType(legacyMessage.type),
      isRead: false, // النظام القديم لا يتتبع القراءة
      attachments: _extractAttachments(legacyMessage),
    );
  }

  /// تحويل رسالة من النظام الجديد للقديم
  static legacy.MessageModel convertEnhancedMessage(
    enhanced.EnhancedChatMessage enhancedMessage,
  ) {
    return legacy.MessageModel(
      id: enhancedMessage.id,
      message: enhancedMessage.content,
      senderId: enhancedMessage.senderId,
      senderName: enhancedMessage.senderName,
      timestamp: enhancedMessage.timestamp,
      type: _convertEnhancedMessageType(enhancedMessage.type),
      imageUrl:
          enhancedMessage.attachments.isNotEmpty
              ? enhancedMessage.attachments.first
              : null,
    );
  }

  /// تحويل نوع الرسالة من القديم للجديد
  static enhanced.MessageType _convertMessageType(
    legacy.MessageType legacyType,
  ) {
    switch (legacyType) {
      case legacy.MessageType.text:
        return enhanced.MessageType.text;
      case legacy.MessageType.image:
        return enhanced.MessageType.image;
      case legacy.MessageType.file:
        return enhanced.MessageType.file;
      case legacy.MessageType.system:
        return enhanced.MessageType.system;
    }
  }

  /// تحويل نوع الرسالة من الجديد للقديم
  static legacy.MessageType _convertEnhancedMessageType(
    enhanced.MessageType enhancedType,
  ) {
    switch (enhancedType) {
      case enhanced.MessageType.text:
        return legacy.MessageType.text;
      case enhanced.MessageType.image:
        return legacy.MessageType.image;
      case enhanced.MessageType.file:
        return legacy.MessageType.file;
      case enhanced.MessageType.voice:
        return legacy.MessageType.text; // النظام القديم لا يدعم الصوت
      case enhanced.MessageType.video:
        return legacy.MessageType.text; // النظام القديم لا يدعم الفيديو
      case enhanced.MessageType.system:
        return legacy.MessageType.system;
    }
  }

  /// استخراج المرفقات من الرسالة القديمة
  static List<String> _extractAttachments(legacy.MessageModel legacyMessage) {
    final attachments = <String>[];

    if (legacyMessage.imageUrl != null) {
      attachments.add(legacyMessage.imageUrl!);
    }

    if (legacyMessage.fileUrl != null) {
      attachments.add(legacyMessage.fileUrl!);
    }

    return attachments;
  }

  /// ترحيل البيانات من النظام القديم للجديد
  Future<void> migrateFromLegacySystem(String chatRoomId) async {
    try {
      if (kDebugMode) {
        debugPrint('🔄 بدء ترحيل البيانات للغرفة: $chatRoomId');
      }

      // الحصول على الرسائل من النظام القديم
      final legacyMessages = await _getLegacyMessages(chatRoomId);

      if (legacyMessages.isEmpty) {
        if (kDebugMode) {
          debugPrint('ℹ️ لا توجد رسائل للترحيل في الغرفة: $chatRoomId');
        }
        return;
      }

      // تحويل الرسائل للنظام الجديد
      final enhancedMessages =
          legacyMessages.map((msg) => convertLegacyMessage(msg)).toList();

      // حفظ الرسائل في النظام الجديد
      for (final message in enhancedMessages) {
        await EnhancedChatService.sendMessage(
          chatRoomId: chatRoomId,
          content: message.content,
          type: message.type,
          attachments: message.attachments,
        );
      }

      if (kDebugMode) {
        debugPrint(
          '✅ تم ترحيل ${enhancedMessages.length} رسالة للغرفة: $chatRoomId',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في ترحيل البيانات: $e');
      }
    }
  }

  /// الحصول على الرسائل من النظام القديم
  Future<List<legacy.MessageModel>> _getLegacyMessages(
    String chatRoomId,
  ) async {
    try {
      // استخدام النظام القديم للحصول على الرسائل
      final stream = ChatService.getMessagesStream(chatRoomId);
      final snapshot = await stream.first;
      return snapshot;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في الحصول على الرسائل القديمة: $e');
      }
      return [];
    }
  }

  /// تبديل بين النظام القديم والجديد
  void toggleEnhancedMode(bool enabled) {
    _isEnhancedModeEnabled = enabled;

    if (kDebugMode) {
      debugPrint('🔄 تم ${enabled ? 'تمكين' : 'تعطيل'} النظام المحسن');
    }
  }

  /// الحصول على إحصائيات النظام
  Map<String, dynamic> getSystemStats() {
    final performanceStats = ChatPerformanceManager().getPerformanceStats();

    return {
      'enhancedModeEnabled': _isEnhancedModeEnabled,
      'isInitialized': _isInitialized,
      'performanceStats': performanceStats,
      'systemVersion': '2.0.0',
      'features': [
        'pagination',
        'memory_management',
        'smooth_animations',
        'performance_optimization',
      ],
    };
  }

  /// تنظيف النظام
  void cleanup() {
    try {
      ChatPerformanceManager().dispose();
      EnhancedChatService.dispose();

      _isInitialized = false;

      if (kDebugMode) {
        debugPrint('🧹 تم تنظيف نظام التكامل');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في تنظيف النظام: $e');
      }
    }
  }

  /// اختبار النظام
  Future<bool> runSystemTest() async {
    try {
      if (kDebugMode) {
        debugPrint('🧪 بدء اختبار النظام...');
      }

      // اختبار تهيئة النظام
      if (!_isInitialized) {
        await initialize();
      }

      // اختبار تحميل الرسائل
      final testRoomId = 'test_room_${DateTime.now().millisecondsSinceEpoch}';
      await EnhancedChatService.loadInitialMessages(testRoomId);

      // اختبار إرسال رسالة
      final sendResult = await EnhancedChatService.sendMessage(
        chatRoomId: testRoomId,
        content: 'رسالة اختبار',
      );

      // اختبار مدير الأداء
      ChatPerformanceManager().registerRoom(testRoomId);
      final stats = ChatPerformanceManager().getPerformanceStats();

      // تنظيف الاختبار
      ChatPerformanceManager().unregisterRoom(testRoomId);

      final success = sendResult && stats.isNotEmpty;

      if (kDebugMode) {
        debugPrint(success ? '✅ نجح اختبار النظام' : '❌ فشل اختبار النظام');
      }

      return success;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في اختبار النظام: $e');
      }
      return false;
    }
  }

  /// طباعة تقرير شامل
  void printSystemReport() {
    if (!kDebugMode) return;

    final stats = getSystemStats();

    debugPrint('📊 تقرير نظام الدردشة المحسن:');
    debugPrint('=' * 50);
    debugPrint('🔧 الحالة العامة:');
    debugPrint(
      '   • النظام المحسن: ${stats['enhancedModeEnabled'] ? 'مُفعل' : 'معطل'}',
    );
    debugPrint(
      '   • التهيئة: ${stats['isInitialized'] ? 'مكتملة' : 'غير مكتملة'}',
    );
    debugPrint('   • الإصدار: ${stats['systemVersion']}');

    debugPrint('\n🚀 المميزات المتاحة:');
    final features = stats['features'] as List<String>;
    for (final feature in features) {
      debugPrint('   • $feature');
    }

    debugPrint('\n📈 إحصائيات الأداء:');
    ChatPerformanceManager().printPerformanceReport();

    debugPrint('=' * 50);
  }
}
