import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/offline_provider.dart';
import '../widgets/offline_indicator.dart';
import '../models/pdf_file.dart';

/// شاشة إعدادات الوضع غير المتصل
class OfflineSettingsScreen extends StatefulWidget {
  const OfflineSettingsScreen({super.key});

  @override
  State<OfflineSettingsScreen> createState() => _OfflineSettingsScreenState();
}

class _OfflineSettingsScreenState extends State<OfflineSettingsScreen> {
  bool _autoDownloadPdfs = false;
  bool _syncOnWifiOnly = true;
  bool _keepMessagesOffline = true;
  int _maxOfflineMessages = 500;
  double _maxStorageSize = 100; // MB

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDark ? Colors.grey.shade900 : Colors.grey.shade50,
      appBar: AppBar(
        title: const Text('إعدادات الوضع غير المتصل'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          Consumer<OfflineProvider>(
            builder: (context, offline, child) {
              return ConnectionStatusDot(
                margin: const EdgeInsets.only(left: 16),
              );
            },
          ),
        ],
      ),
      body: Consumer<OfflineProvider>(
        builder: (context, offline, child) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // حالة الاتصال
                _buildStatusCard(offline, theme, isDark),

                const SizedBox(height: 20),

                // إعدادات المزامنة
                _buildSyncSettings(offline, theme, isDark),

                const SizedBox(height: 20),

                // إعدادات التخزين
                _buildStorageSettings(offline, theme, isDark),

                const SizedBox(height: 20),

                // إدارة ملفات PDF
                _buildPdfManagement(offline, theme, isDark),

                const SizedBox(height: 20),

                // إحصائيات التخزين
                _buildStorageStats(offline, theme, isDark),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildStatusCard(
    OfflineProvider offline,
    ThemeData theme,
    bool isDark,
  ) {
    final status = offline.currentStatus;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors:
              status.isOnline
                  ? [Colors.green.shade400, Colors.green.shade600]
                  : [Colors.orange.shade400, Colors.orange.shade600],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: (status.isOnline ? Colors.green : Colors.orange).withValues(
              alpha: 0.3,
            ),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                status.isOnline ? Icons.wifi_rounded : Icons.wifi_off_rounded,
                color: Colors.white,
                size: 28,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  status.isOnline ? 'متصل بالإنترنت' : 'غير متصل',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              if (offline.isSyncing)
                const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    color: Colors.white,
                    strokeWidth: 2,
                  ),
                ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            offline.getStatusMessage(),
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.9),
              fontSize: 14,
            ),
          ),
          if (status.pendingSyncItems > 0) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                '${status.pendingSyncItems} عنصر في انتظار المزامنة',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSyncSettings(
    OfflineProvider offline,
    ThemeData theme,
    bool isDark,
  ) {
    return _buildSettingsCard(
      title: 'إعدادات المزامنة',
      icon: Icons.sync_rounded,
      theme: theme,
      isDark: isDark,
      children: [
        SwitchListTile(
          title: const Text('المزامنة على WiFi فقط'),
          subtitle: const Text('توفير بيانات الجوال'),
          value: _syncOnWifiOnly,
          onChanged: (value) {
            setState(() {
              _syncOnWifiOnly = value;
            });
          },
        ),
        const Divider(height: 1),
        ListTile(
          title: const Text('مزامنة فورية'),
          subtitle: Text(
            offline.isSyncing ? 'جاري المزامنة...' : 'مزامنة البيانات الآن',
          ),
          trailing:
              offline.isSyncing
                  ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                  : const Icon(Icons.sync_rounded),
          onTap:
              offline.isSyncing
                  ? null
                  : () async {
                    final result = await offline.forceSync();
                    if (mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(result.message),
                          backgroundColor:
                              result.success ? Colors.green : Colors.red,
                        ),
                      );
                    }
                  },
        ),
      ],
    );
  }

  Widget _buildStorageSettings(
    OfflineProvider offline,
    ThemeData theme,
    bool isDark,
  ) {
    return _buildSettingsCard(
      title: 'إعدادات التخزين',
      icon: Icons.storage_rounded,
      theme: theme,
      isDark: isDark,
      children: [
        SwitchListTile(
          title: const Text('حفظ الرسائل محلياً'),
          subtitle: const Text('للوصول إليها بدون إنترنت'),
          value: _keepMessagesOffline,
          onChanged: (value) {
            setState(() {
              _keepMessagesOffline = value;
            });
          },
        ),
        const Divider(height: 1),
        ListTile(
          title: const Text('عدد الرسائل المحفوظة'),
          subtitle: Text('$_maxOfflineMessages رسالة كحد أقصى'),
          trailing: const Icon(Icons.edit_rounded),
          onTap:
              () => _showNumberPicker(
                title: 'عدد الرسائل المحفوظة',
                value: _maxOfflineMessages,
                min: 100,
                max: 2000,
                step: 100,
                onChanged: (value) {
                  setState(() {
                    _maxOfflineMessages = value;
                  });
                },
              ),
        ),
        const Divider(height: 1),
        ListTile(
          title: const Text('حد التخزين'),
          subtitle: Text('${_maxStorageSize.toInt()} ميجابايت كحد أقصى'),
          trailing: const Icon(Icons.edit_rounded),
          onTap:
              () => _showSliderPicker(
                title: 'حد التخزين (ميجابايت)',
                value: _maxStorageSize,
                min: 50,
                max: 500,
                onChanged: (value) {
                  setState(() {
                    _maxStorageSize = value;
                  });
                },
              ),
        ),
      ],
    );
  }

  Widget _buildPdfManagement(
    OfflineProvider offline,
    ThemeData theme,
    bool isDark,
  ) {
    final offlinePdfs = offline.getOfflinePdfs();

    return _buildSettingsCard(
      title: 'إدارة ملفات PDF',
      icon: Icons.picture_as_pdf_rounded,
      theme: theme,
      isDark: isDark,
      children: [
        SwitchListTile(
          title: const Text('تحميل تلقائي للملفات'),
          subtitle: const Text('تحميل ملفات PDF الجديدة تلقائياً'),
          value: _autoDownloadPdfs,
          onChanged: (value) {
            setState(() {
              _autoDownloadPdfs = value;
            });
          },
        ),
        const Divider(height: 1),
        ListTile(
          title: const Text('الملفات المحفوظة'),
          subtitle: Text('${offlinePdfs.length} ملف متاح غير متصل'),
          trailing: const Icon(Icons.folder_rounded),
          onTap: () => _showOfflinePdfsDialog(offline, offlinePdfs),
        ),
        const Divider(height: 1),
        ListTile(
          title: const Text('تنظيف الملفات القديمة'),
          subtitle: const Text('حذف الملفات غير المستخدمة'),
          trailing: const Icon(Icons.cleaning_services_rounded),
          onTap: () => _showCleanupDialog(offline),
        ),
      ],
    );
  }

  Widget _buildStorageStats(
    OfflineProvider offline,
    ThemeData theme,
    bool isDark,
  ) {
    final stats = offline.getStorageStatistics();

    return _buildSettingsCard(
      title: 'إحصائيات التخزين',
      icon: Icons.analytics_rounded,
      theme: theme,
      isDark: isDark,
      children: [
        _buildStatRow('الرسائل المحفوظة', '${stats['messages_count']}'),
        _buildStatRow('ملفات PDF', '${stats['offline_pdfs_count']}'),
        _buildStatRow('حجم ملفات PDF', stats['formatted_pdf_size'] ?? '0 B'),
        _buildStatRow('عناصر التخزين المؤقت', '${stats['cache_items_count']}'),
      ],
    );
  }

  Widget _buildSettingsCard({
    required String title,
    required IconData icon,
    required ThemeData theme,
    required bool isDark,
    required List<Widget> children,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: isDark ? Colors.grey.shade800 : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.blue.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: Colors.blue, size: 20),
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: isDark ? Colors.white : Colors.grey.shade800,
                  ),
                ),
              ],
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(value, style: const TextStyle(fontWeight: FontWeight.w600)),
        ],
      ),
    );
  }

  void _showNumberPicker({
    required String title,
    required int value,
    required int min,
    required int max,
    required int step,
    required Function(int) onChanged,
  }) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(title),
            content: StatefulBuilder(
              builder: (context, setState) {
                return Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text('$value'),
                    Slider(
                      value: value.toDouble(),
                      min: min.toDouble(),
                      max: max.toDouble(),
                      divisions: (max - min) ~/ step,
                      onChanged: (newValue) {
                        setState(() {
                          value = newValue.round();
                        });
                      },
                    ),
                  ],
                );
              },
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () {
                  onChanged(value);
                  Navigator.pop(context);
                },
                child: const Text('حفظ'),
              ),
            ],
          ),
    );
  }

  void _showSliderPicker({
    required String title,
    required double value,
    required double min,
    required double max,
    required Function(double) onChanged,
  }) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(title),
            content: StatefulBuilder(
              builder: (context, setState) {
                return Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text('${value.toInt()} MB'),
                    Slider(
                      value: value,
                      min: min,
                      max: max,
                      divisions: ((max - min) / 10).round(),
                      onChanged: (newValue) {
                        setState(() {
                          value = newValue;
                        });
                      },
                    ),
                  ],
                );
              },
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () {
                  onChanged(value);
                  Navigator.pop(context);
                },
                child: const Text('حفظ'),
              ),
            ],
          ),
    );
  }

  void _showOfflinePdfsDialog(OfflineProvider offline, List<PdfFile> pdfs) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('الملفات المحفوظة'),
            content: SizedBox(
              width: double.maxFinite,
              height: 300,
              child: ListView.builder(
                itemCount: pdfs.length,
                itemBuilder: (context, index) {
                  final pdf = pdfs[index];
                  return ListTile(
                    leading: const Icon(Icons.picture_as_pdf_rounded),
                    title: Text(pdf.name),
                    subtitle: Text(
                      '${pdf.formattedFileSize} • ${pdf.categoryName}',
                    ),
                    trailing: IconButton(
                      icon: const Icon(Icons.delete_rounded),
                      onPressed: () async {
                        await offline.removeOfflinePdf(pdf.id);
                        if (mounted) {
                          Navigator.pop(context);
                          setState(() {});
                        }
                      },
                    ),
                  );
                },
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إغلاق'),
              ),
            ],
          ),
    );
  }

  void _showCleanupDialog(OfflineProvider offline) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تنظيف البيانات'),
            content: const Text('هل تريد حذف البيانات القديمة وغير المستخدمة؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () async {
                  Navigator.pop(context);
                  await offline.cleanupOldData();
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('تم تنظيف البيانات بنجاح'),
                        backgroundColor: Colors.green,
                      ),
                    );
                  }
                },
                child: const Text('تنظيف'),
              ),
            ],
          ),
    );
  }
}
