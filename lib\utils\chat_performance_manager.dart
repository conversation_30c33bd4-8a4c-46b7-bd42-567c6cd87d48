import 'dart:async';
import 'package:flutter/foundation.dart';
import '../services/enhanced_chat_service.dart';

/// مدير الأداء والذاكرة للدردشة
class ChatPerformanceManager {
  static final ChatPerformanceManager _instance =
      ChatPerformanceManager._internal();
  factory ChatPerformanceManager() => _instance;
  ChatPerformanceManager._internal();

  // إعدادات الأداء
  static const int maxMessagesInMemory = 200;
  static const int maxMessagesInDatabase = 200;
  static const int cleanupInterval = 300; // 5 دقائق بالثواني
  static const int memoryCheckInterval = 60; // دقيقة واحدة

  // Timers للتنظيف التلقائي
  Timer? _cleanupTimer;
  Timer? _memoryCheckTimer;

  // إحصائيات الأداء
  final Map<String, int> _roomMessageCounts = {};
  final Map<String, DateTime> _lastCleanupTimes = {};
  int _totalMessagesInMemory = 0;

  /// تهيئة مدير الأداء
  void initialize() {
    _startCleanupTimer();
    _startMemoryCheckTimer();

    if (kDebugMode) {
      print('🚀 تم تهيئة مدير أداء الدردشة');
    }
  }

  /// بدء مؤقت التنظيف التلقائي
  void _startCleanupTimer() {
    _cleanupTimer?.cancel();
    _cleanupTimer = Timer.periodic(
      const Duration(seconds: cleanupInterval),
      (_) => _performScheduledCleanup(),
    );
  }

  /// بدء مؤقت فحص الذاكرة
  void _startMemoryCheckTimer() {
    _memoryCheckTimer?.cancel();
    _memoryCheckTimer = Timer.periodic(
      const Duration(seconds: memoryCheckInterval),
      (_) => _checkMemoryUsage(),
    );
  }

  /// تنظيف مجدول للرسائل القديمة
  Future<void> _performScheduledCleanup() async {
    try {
      final now = DateTime.now();

      for (final roomId in _roomMessageCounts.keys) {
        final lastCleanup = _lastCleanupTimes[roomId];

        // تنظيف كل 5 دقائق لكل غرفة
        if (lastCleanup == null ||
            now.difference(lastCleanup).inSeconds >= cleanupInterval) {
          await _cleanupRoomMessages(roomId);
          _lastCleanupTimes[roomId] = now;
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في التنظيف المجدول: $e');
      }
    }
  }

  /// فحص استخدام الذاكرة
  void _checkMemoryUsage() {
    try {
      _totalMessagesInMemory = 0;

      for (final roomId in _roomMessageCounts.keys) {
        final messages = EnhancedChatService.getCachedMessages(roomId);
        _roomMessageCounts[roomId] = messages.length;
        _totalMessagesInMemory += messages.length;
      }

      if (kDebugMode) {
        print('📊 إجمالي الرسائل في الذاكرة: $_totalMessagesInMemory');
      }

      // إذا تجاوز الحد المسموح، قم بالتنظيف الفوري
      if (_totalMessagesInMemory > maxMessagesInMemory * 1.5) {
        _performEmergencyCleanup();
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في فحص الذاكرة: $e');
      }
    }
  }

  /// تنظيف طارئ للذاكرة
  Future<void> _performEmergencyCleanup() async {
    if (kDebugMode) {
      print('🚨 تنظيف طارئ للذاكرة - الرسائل: $_totalMessagesInMemory');
    }

    try {
      // ترتيب الغرف حسب عدد الرسائل (الأكثر أولاً)
      final sortedRooms =
          _roomMessageCounts.entries.toList()
            ..sort((a, b) => b.value.compareTo(a.value));

      // تنظيف الغرف التي تحتوي على أكثر رسائل
      for (final entry in sortedRooms.take(3)) {
        await _cleanupRoomMessages(entry.key, aggressive: true);
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في التنظيف الطارئ: $e');
      }
    }
  }

  /// تنظيف رسائل غرفة محددة
  Future<void> _cleanupRoomMessages(
    String roomId, {
    bool aggressive = false,
  }) async {
    try {
      final messages = EnhancedChatService.getCachedMessages(roomId);

      if (messages.isEmpty) return;

      final targetCount =
          aggressive
              ? (maxMessagesInMemory * 0.5)
                  .round() // 50% في التنظيف الطارئ
              : maxMessagesInMemory;

      if (messages.length > targetCount) {
        // الاحتفاظ بأحدث الرسائل فقط
        final messagesToKeep = messages.take(targetCount).toList();

        // تحديث الـ cache
        EnhancedChatService.clearCacheForRoom(roomId);

        if (kDebugMode) {
          print(
            '🗑️ تم تنظيف ${messages.length - messagesToKeep.length} رسالة من الغرفة: $roomId',
          );
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تنظيف رسائل الغرفة $roomId: $e');
      }
    }
  }

  /// تسجيل غرفة جديدة للمراقبة
  void registerRoom(String roomId) {
    if (!_roomMessageCounts.containsKey(roomId)) {
      _roomMessageCounts[roomId] = 0;
      _lastCleanupTimes[roomId] = DateTime.now();

      if (kDebugMode) {
        print('📝 تم تسجيل غرفة للمراقبة: $roomId');
      }
    }
  }

  /// إلغاء تسجيل غرفة
  void unregisterRoom(String roomId) {
    _roomMessageCounts.remove(roomId);
    _lastCleanupTimes.remove(roomId);
    EnhancedChatService.clearCacheForRoom(roomId);

    if (kDebugMode) {
      print('❌ تم إلغاء تسجيل الغرفة: $roomId');
    }
  }

  /// تحديث عدد الرسائل لغرفة
  void updateMessageCount(String roomId, int count) {
    _roomMessageCounts[roomId] = count;
  }

  /// الحصول على إحصائيات الأداء
  Map<String, dynamic> getPerformanceStats() {
    return {
      'totalMessagesInMemory': _totalMessagesInMemory,
      'registeredRooms': _roomMessageCounts.length,
      'roomMessageCounts': Map.from(_roomMessageCounts),
      'lastCleanupTimes': _lastCleanupTimes.map(
        (key, value) => MapEntry(key, value.toIso8601String()),
      ),
      'memoryUsagePercentage':
          (_totalMessagesInMemory /
              (maxMessagesInMemory * _roomMessageCounts.length)) *
          100,
    };
  }

  /// تنظيف شامل لجميع الغرف
  Future<void> cleanupAllRooms() async {
    if (kDebugMode) {
      print('🧹 بدء التنظيف الشامل لجميع الغرف');
    }

    for (final roomId in _roomMessageCounts.keys) {
      await _cleanupRoomMessages(roomId);
    }

    if (kDebugMode) {
      print('✅ انتهى التنظيف الشامل');
    }
  }

  /// تحسين الأداء للغرفة النشطة
  void optimizeActiveRoom(String activeRoomId) {
    // إعطاء أولوية أعلى للغرفة النشطة
    for (final roomId in _roomMessageCounts.keys) {
      if (roomId != activeRoomId) {
        // تقليل عدد الرسائل المحفوظة للغرف غير النشطة
        final messages = EnhancedChatService.getCachedMessages(roomId);
        if (messages.length > 50) {
          // الاحتفاظ بـ 50 رسالة فقط للغرف غير النشطة
          _cleanupRoomMessages(roomId, aggressive: true);
        }
      }
    }
  }

  /// إيقاف مدير الأداء
  void dispose() {
    _cleanupTimer?.cancel();
    _memoryCheckTimer?.cancel();
    _roomMessageCounts.clear();
    _lastCleanupTimes.clear();

    if (kDebugMode) {
      print('🛑 تم إيقاف مدير أداء الدردشة');
    }
  }

  /// طباعة تقرير الأداء
  void printPerformanceReport() {
    if (!kDebugMode) return;

    final stats = getPerformanceStats();
    print('📊 تقرير أداء الدردشة:');
    print('   • إجمالي الرسائل في الذاكرة: ${stats['totalMessagesInMemory']}');
    print('   • عدد الغرف المسجلة: ${stats['registeredRooms']}');
    print(
      '   • نسبة استخدام الذاكرة: ${stats['memoryUsagePercentage'].toStringAsFixed(1)}%',
    );
    print('   • تفاصيل الغرف:');

    final roomCounts = stats['roomMessageCounts'] as Map<String, int>;
    roomCounts.forEach((roomId, count) {
      print('     - $roomId: $count رسالة');
    });
  }
}
