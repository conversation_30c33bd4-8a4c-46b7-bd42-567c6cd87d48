import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import '../models/community_post.dart';
import '../services/community_service.dart';
import '../providers/theme_provider.dart';
import '../providers/auth_provider.dart';
import 'package:provider/provider.dart';

/// صفحة تفاصيل المنشور مع نظام تعليقات مثل TikTok
/// الردود مخفية بشكل افتراضي ولا تظهر إلا عند الضغط على زر "عرض الردود"
class PostDetailScreen extends StatefulWidget {
  final CommunityPost post;

  const PostDetailScreen({super.key, required this.post});

  @override
  State<PostDetailScreen> createState() => _PostDetailScreenState();
}

class _PostDetailScreenState extends State<PostDetailScreen>
    with TickerProviderStateMixin {
  // متحكمات النص والتركيز
  final TextEditingController _commentController = TextEditingController();
  final FocusNode _commentFocusNode = FocusNode();

  // إدارة حالة التعليقات
  List<CommunityComment> _comments = [];
  bool _isLoading = true;
  bool _isSubmitting = false;

  // إدارة حالة الردود - مجموعة IDs للتعليقات المفتوحة
  final Set<String> _expandedComments = <String>{};

  // إدارة حالة الرد
  bool _isReplyMode = false;
  CommunityComment? _replyingToComment;

  // متحكمات الانيميشن
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadComments();
  }

  @override
  void dispose() {
    _commentController.dispose();
    _commentFocusNode.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  /// تهيئة الانيميشن
  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
    );

    _fadeController.forward();
    _slideController.forward();
  }

  /// تحميل التعليقات من Firebase
  Future<void> _loadComments() async {
    try {
      setState(() => _isLoading = true);

      // تأكد من أن جميع الردود مخفية عند التحميل
      _expandedComments.clear();

      // جلب التعليقات من الخدمة
      final commentsStream = CommunityService.getCommentsStream(widget.post.id);

      // الاستماع للتعليقات
      commentsStream.listen(
        (comments) {
          if (mounted) {
            setState(() {
              _comments = comments;
              _isLoading = false;
            });
          }
        },
        onError: (error) {
          if (mounted) {
            setState(() => _isLoading = false);
            _showErrorSnackBar('خطأ في تحميل التعليقات');
          }
        },
      );
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        _showErrorSnackBar('خطأ في تحميل التعليقات');
      }
    }
  }

  /// إضافة تعليق أو رد جديد
  Future<void> _addComment() async {
    final content = _commentController.text.trim();
    if (content.isEmpty) return;

    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    if (!authProvider.isAuthenticated) {
      _showErrorSnackBar('يجب تسجيل الدخول أولاً');
      return;
    }

    try {
      setState(() => _isSubmitting = true);

      await CommunityService.addComment(
        postId: widget.post.id,
        content: content,
        parentCommentId: _isReplyMode ? _replyingToComment?.id : null,
        replyToUserId: _isReplyMode ? _replyingToComment?.authorId : null,
        replyToUserName: _isReplyMode ? _replyingToComment?.authorName : null,
      );

      // مسح النص وإلغاء وضع الرد
      _commentController.clear();
      _cancelReply();

      // إخفاء لوحة المفاتيح
      if (mounted) {
        FocusScope.of(context).unfocus();
      }

      _showSuccessSnackBar(
        _isReplyMode ? 'تم إضافة الرد بنجاح' : 'تم إضافة التعليق بنجاح',
      );
    } catch (e) {
      _showErrorSnackBar('خطأ في إضافة التعليق');
    } finally {
      if (mounted) {
        setState(() => _isSubmitting = false);
      }
    }
  }

  /// بدء وضع الرد على تعليق
  void _startReply(CommunityComment comment) {
    setState(() {
      _isReplyMode = true;
      _replyingToComment = comment;
    });
    _commentFocusNode.requestFocus();
  }

  /// إلغاء وضع الرد
  void _cancelReply() {
    setState(() {
      _isReplyMode = false;
      _replyingToComment = null;
    });
  }

  /// تبديل حالة عرض الردود لتعليق معين
  void _toggleReplies(String commentId) {
    setState(() {
      if (_expandedComments.contains(commentId)) {
        _expandedComments.remove(commentId);
      } else {
        _expandedComments.add(commentId);
      }
    });
  }

  /// عرض رسالة خطأ
  void _showErrorSnackBar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// عرض رسالة نجاح
  void _showSuccessSnackBar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Scaffold(
          backgroundColor:
              themeProvider.isDarkMode
                  ? const Color(0xFF1A1A1A)
                  : const Color(0xFFF8F9FA),
          appBar: _buildAppBar(themeProvider),
          body: FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: Column(
                children: [
                  // محتوى المنشور
                  _buildPostContent(themeProvider),

                  // قائمة التعليقات
                  Expanded(child: _buildCommentsList(themeProvider)),

                  // شريط إدخال التعليق
                  _buildCommentInput(themeProvider),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء شريط التطبيق
  PreferredSizeWidget _buildAppBar(ThemeProvider themeProvider) {
    return AppBar(
      backgroundColor:
          themeProvider.isDarkMode ? const Color(0xFF2D2D2D) : Colors.white,
      elevation: 0,
      leading: IconButton(
        icon: Icon(
          Icons.arrow_back_ios,
          color: themeProvider.isDarkMode ? Colors.white : Colors.black,
        ),
        onPressed: () => Navigator.pop(context),
      ),
      title: Text(
        'التعليقات',
        style: GoogleFonts.cairo(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: themeProvider.isDarkMode ? Colors.white : Colors.black,
        ),
      ),
      centerTitle: true,
    );
  }

  /// بناء محتوى المنشور المحسن
  Widget _buildPostContent(ThemeProvider themeProvider) {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUserId = authProvider.firebaseUser?.uid ?? '';
    final isLiked = widget.post.likedBy.contains(currentUserId);
    final likesCount = widget.post.likedBy.length;

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color:
            themeProvider.isDarkMode ? const Color(0xFF2D2D2D) : Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color:
              themeProvider.isDarkMode
                  ? const Color(0xFF374151)
                  : const Color(0xFFF3F4F6),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.12),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // معلومات المؤلف
          Row(
            children: [
              Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: const LinearGradient(
                    colors: [Color(0xFF6366F1), Color(0xFF8B5CF6)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
                child: CircleAvatar(
                  radius: 22,
                  backgroundColor: Colors.transparent,
                  child: Text(
                    widget.post.authorName.isNotEmpty
                        ? widget.post.authorName[0].toUpperCase()
                        : 'م',
                    style: GoogleFonts.cairo(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.post.authorName,
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.w700,
                        color:
                            themeProvider.isDarkMode
                                ? Colors.white
                                : Colors.black,
                      ),
                    ),
                    Text(
                      _formatTimestamp(widget.post.timestamp),
                      style: GoogleFonts.cairo(
                        fontSize: 13,
                        color:
                            themeProvider.isDarkMode
                                ? Colors.grey[400]
                                : Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // محتوى المنشور
          Text(
            widget.post.content,
            style: GoogleFonts.cairo(
              fontSize: 15,
              color: themeProvider.isDarkMode ? Colors.white : Colors.black,
              height: 1.6,
            ),
          ),
          const SizedBox(height: 20),

          // إحصائيات المنشور
          if (likesCount > 0 || _comments.isNotEmpty)
            Container(
              padding: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color:
                        themeProvider.isDarkMode
                            ? const Color(0xFF374151)
                            : const Color(0xFFE5E7EB),
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                children: [
                  if (likesCount > 0) ...[
                    Icon(
                      Icons.favorite,
                      size: 16,
                      color: const Color(0xFFEF4444),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '$likesCount',
                      style: GoogleFonts.cairo(
                        fontSize: 13,
                        color:
                            themeProvider.isDarkMode
                                ? Colors.grey[400]
                                : Colors.grey[600],
                      ),
                    ),
                  ],
                  const Spacer(),
                  if (_comments.isNotEmpty)
                    Text(
                      '${_comments.length} ${_comments.length == 1 ? 'تعليق' : 'تعليقات'}',
                      style: GoogleFonts.cairo(
                        fontSize: 13,
                        color:
                            themeProvider.isDarkMode
                                ? Colors.grey[400]
                                : Colors.grey[600],
                      ),
                    ),
                ],
              ),
            ),
          const SizedBox(height: 12),

          // أزرار التفاعل مع المنشور
          Row(
            children: [
              _buildPostActionButton(
                icon:
                    isLiked
                        ? Icons.favorite_rounded
                        : Icons.favorite_border_rounded,
                label: 'إعجاب',
                isActive: isLiked,
                onTap: () => _likePost(),
                themeProvider: themeProvider,
              ),
              const SizedBox(width: 24),
              _buildPostActionButton(
                icon: Icons.chat_bubble_outline_rounded,
                label: 'تعليق',
                isActive: false,
                onTap: () => _commentFocusNode.requestFocus(),
                themeProvider: themeProvider,
              ),
              const SizedBox(width: 24),
              _buildPostActionButton(
                icon: Icons.share_rounded,
                label: 'مشاركة',
                isActive: false,
                onTap: () => _sharePost(),
                themeProvider: themeProvider,
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء زر تفاعل المنشور
  Widget _buildPostActionButton({
    required IconData icon,
    required String label,
    required bool isActive,
    required VoidCallback onTap,
    required ThemeProvider themeProvider,
  }) {
    return Expanded(
      child: GestureDetector(
        onTap: () {
          HapticFeedback.lightImpact();
          onTap();
        },
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            color:
                isActive
                    ? (icon == Icons.favorite
                            ? const Color(0xFFEF4444)
                            : const Color(0xFF6366F1))
                        .withValues(alpha: 0.1)
                    : Colors.transparent,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              AnimatedSwitcher(
                duration: const Duration(milliseconds: 200),
                child: Icon(
                  icon,
                  key: ValueKey('$icon-$isActive'),
                  size: 20,
                  color:
                      isActive
                          ? (icon == Icons.favorite
                              ? const Color(0xFFEF4444)
                              : const Color(0xFF6366F1))
                          : themeProvider.isDarkMode
                          ? const Color(0xFF9CA3AF)
                          : const Color(0xFF6B7280),
                ),
              ),
              const SizedBox(width: 6),
              AnimatedDefaultTextStyle(
                duration: const Duration(milliseconds: 200),
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  fontWeight: isActive ? FontWeight.w600 : FontWeight.w500,
                  color:
                      isActive
                          ? (icon == Icons.favorite
                              ? const Color(0xFFEF4444)
                              : const Color(0xFF6366F1))
                          : themeProvider.isDarkMode
                          ? const Color(0xFF9CA3AF)
                          : const Color(0xFF6B7280),
                ),
                child: Text(label),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// إعجاب بالمنشور
  Future<void> _likePost() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    if (!authProvider.isAuthenticated) {
      _showErrorSnackBar('يجب تسجيل الدخول أولاً');
      return;
    }

    final currentUserId = authProvider.firebaseUser?.uid ?? '';
    final isLiked = widget.post.likedBy.contains(currentUserId);

    try {
      // تحديث محلي فوري
      setState(() {
        if (isLiked) {
          widget.post.likedBy.remove(currentUserId);
        } else {
          widget.post.likedBy.add(currentUserId);
        }
      });

      // تحديث في Firebase
      final success = await CommunityService.togglePostLike(widget.post.id);

      if (!success) {
        // إعادة التحديث في حالة الفشل
        setState(() {
          if (isLiked) {
            widget.post.likedBy.add(currentUserId);
          } else {
            widget.post.likedBy.remove(currentUserId);
          }
        });
        _showErrorSnackBar('خطأ في تحديث الإعجاب');
      }
    } catch (e) {
      // إعادة التحديث في حالة الخطأ
      setState(() {
        if (isLiked) {
          widget.post.likedBy.add(currentUserId);
        } else {
          widget.post.likedBy.remove(currentUserId);
        }
      });
      _showErrorSnackBar('خطأ في تحديث الإعجاب');
    }
  }

  /// مشاركة المنشور
  void _sharePost() {
    _showSuccessSnackBar('ميزة المشاركة قريباً');
  }

  /// بناء قائمة التعليقات
  Widget _buildCommentsList(ThemeProvider themeProvider) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_comments.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.chat_bubble_outline,
              size: 64,
              color:
                  themeProvider.isDarkMode
                      ? Colors.grey[600]
                      : Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد تعليقات بعد',
              style: GoogleFonts.cairo(
                fontSize: 16,
                color:
                    themeProvider.isDarkMode
                        ? Colors.grey[400]
                        : Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'كن أول من يعلق على هذا المنشور',
              style: GoogleFonts.cairo(
                fontSize: 14,
                color:
                    themeProvider.isDarkMode
                        ? Colors.grey[500]
                        : Colors.grey[500],
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: _comments.length,
      itemBuilder: (context, index) {
        final comment = _comments[index];
        return _buildCommentItem(comment, themeProvider);
      },
    );
  }

  /// بناء عنصر تعليق واحد
  Widget _buildCommentItem(
    CommunityComment comment,
    ThemeProvider themeProvider,
  ) {
    final hasReplies = comment.replies.isNotEmpty;
    final isExpanded = _expandedComments.contains(comment.id);

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // التعليق الرئيسي
          GestureDetector(
            onLongPress: () => _showDeleteCommentDialog(comment),
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color:
                    themeProvider.isDarkMode
                        ? const Color(0xFF2D2D2D)
                        : Colors.white,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color:
                      themeProvider.isDarkMode
                          ? const Color(0xFF374151)
                          : const Color(0xFFF3F4F6),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.08),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // معلومات المؤلف
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 16,
                        backgroundColor: const Color(0xFF6366F1),
                        child: Text(
                          comment.authorName.isNotEmpty
                              ? comment.authorName[0].toUpperCase()
                              : 'م',
                          style: GoogleFonts.cairo(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              comment.authorName,
                              style: GoogleFonts.cairo(
                                fontSize: 13,
                                fontWeight: FontWeight.w600,
                                color:
                                    themeProvider.isDarkMode
                                        ? Colors.white
                                        : Colors.black,
                              ),
                            ),
                            Text(
                              _formatTimestamp(comment.timestamp),
                              style: GoogleFonts.cairo(
                                fontSize: 11,
                                color:
                                    themeProvider.isDarkMode
                                        ? Colors.grey[400]
                                        : Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),

                  // محتوى التعليق
                  Text(
                    comment.content,
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color:
                          themeProvider.isDarkMode
                              ? Colors.white
                              : Colors.black,
                      height: 1.4,
                    ),
                  ),
                  const SizedBox(height: 12),

                  // أزرار التفاعل
                  Row(
                    children: [
                      _buildLikeButton(comment, themeProvider),
                      const SizedBox(width: 16),
                      _buildActionButton(
                        icon: Icons.reply_rounded,
                        label: 'رد',
                        onTap: () => _startReply(comment),
                        themeProvider: themeProvider,
                      ),
                      // زر عرض الردود (إذا كانت موجودة)
                      if (hasReplies) ...[
                        const SizedBox(width: 16),
                        _buildInlineRepliesToggle(comment, themeProvider),
                      ],
                    ],
                  ),
                ],
              ),
            ),
          ),

          // الردود (إذا كانت مفتوحة)
          if (hasReplies && isExpanded) ...[
            const SizedBox(height: 8),
            ...comment.replies.map(
              (reply) => _buildReplyItem(reply, themeProvider),
            ),
          ],
        ],
      ),
    );
  }

  /// بناء زر الردود المدمج داخل التعليق
  Widget _buildInlineRepliesToggle(
    CommunityComment comment,
    ThemeProvider themeProvider,
  ) {
    final isExpanded = _expandedComments.contains(comment.id);
    final repliesCount = comment.replies.length;

    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        _toggleReplies(comment.id);
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color:
              isExpanded
                  ? const Color(0xFF6366F1).withValues(alpha: 0.1)
                  : themeProvider.isDarkMode
                  ? const Color(0xFF374151)
                  : const Color(0xFFF3F4F6),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color:
                isExpanded
                    ? const Color(0xFF6366F1).withValues(alpha: 0.3)
                    : themeProvider.isDarkMode
                    ? const Color(0xFF4B5563)
                    : const Color(0xFFE5E7EB),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            AnimatedRotation(
              turns: isExpanded ? 0.5 : 0,
              duration: const Duration(milliseconds: 200),
              child: Icon(
                Icons.chat_bubble_outline_rounded,
                size: 14,
                color:
                    isExpanded
                        ? const Color(0xFF6366F1)
                        : themeProvider.isDarkMode
                        ? const Color(0xFF9CA3AF)
                        : const Color(0xFF6B7280),
              ),
            ),
            const SizedBox(width: 6),
            AnimatedDefaultTextStyle(
              duration: const Duration(milliseconds: 200),
              style: GoogleFonts.cairo(
                fontSize: 12,
                color:
                    isExpanded
                        ? const Color(0xFF6366F1)
                        : themeProvider.isDarkMode
                        ? const Color(0xFF9CA3AF)
                        : const Color(0xFF6B7280),
                fontWeight: isExpanded ? FontWeight.w600 : FontWeight.w500,
              ),
              child: Text(
                isExpanded
                    ? 'إخفاء'
                    : '$repliesCount ${repliesCount == 1 ? 'رد' : 'ردود'}',
              ),
            ),
            const SizedBox(width: 4),
            AnimatedRotation(
              turns: isExpanded ? 0.5 : 0,
              duration: const Duration(milliseconds: 200),
              child: Icon(
                Icons.keyboard_arrow_down,
                size: 14,
                color:
                    isExpanded
                        ? const Color(0xFF6366F1)
                        : themeProvider.isDarkMode
                        ? const Color(0xFF9CA3AF)
                        : const Color(0xFF6B7280),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء عنصر رد
  Widget _buildReplyItem(CommunityComment reply, ThemeProvider themeProvider) {
    return GestureDetector(
      onLongPress: () => _showDeleteCommentDialog(reply),
      child: Container(
        margin: const EdgeInsets.only(right: 32, bottom: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color:
              themeProvider.isDarkMode
                  ? const Color(0xFF374151)
                  : const Color(0xFFF9FAFB),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color:
                themeProvider.isDarkMode
                    ? const Color(0xFF4B5563)
                    : const Color(0xFFE5E7EB),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.04),
              blurRadius: 4,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // معلومات المؤلف
            Row(
              children: [
                CircleAvatar(
                  radius: 12,
                  backgroundColor: const Color(0xFF6366F1),
                  child: Text(
                    reply.authorName.isNotEmpty
                        ? reply.authorName[0].toUpperCase()
                        : 'م',
                    style: GoogleFonts.cairo(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 6),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        reply.authorName,
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color:
                              themeProvider.isDarkMode
                                  ? Colors.white
                                  : Colors.black,
                        ),
                      ),
                      Text(
                        _formatTimestamp(reply.timestamp),
                        style: GoogleFonts.cairo(
                          fontSize: 10,
                          color:
                              themeProvider.isDarkMode
                                  ? Colors.grey[400]
                                  : Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            // محتوى الرد
            Text(
              reply.content,
              style: GoogleFonts.cairo(
                fontSize: 13,
                color: themeProvider.isDarkMode ? Colors.white : Colors.black,
                height: 1.4,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء زر الإعجاب المحسن
  Widget _buildLikeButton(
    CommunityComment comment,
    ThemeProvider themeProvider,
  ) {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUserId = authProvider.firebaseUser?.uid ?? '';
    final isLiked = comment.likedBy.contains(currentUserId);
    final likesCount = comment.likedBy.length;

    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        _likeComment(comment.id);
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color:
              isLiked
                  ? const Color(0xFFEF4444).withValues(alpha: 0.1)
                  : Colors.transparent,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            AnimatedSwitcher(
              duration: const Duration(milliseconds: 200),
              child: Icon(
                isLiked
                    ? Icons.favorite_rounded
                    : Icons.favorite_border_rounded,
                key: ValueKey(isLiked),
                size: 16,
                color:
                    isLiked
                        ? const Color(0xFFEF4444)
                        : themeProvider.isDarkMode
                        ? const Color(0xFF9CA3AF)
                        : const Color(0xFF6B7280),
              ),
            ),
            if (likesCount > 0) ...[
              const SizedBox(width: 4),
              AnimatedDefaultTextStyle(
                duration: const Duration(milliseconds: 200),
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  color:
                      isLiked
                          ? const Color(0xFFEF4444)
                          : themeProvider.isDarkMode
                          ? const Color(0xFF9CA3AF)
                          : const Color(0xFF6B7280),
                  fontWeight: isLiked ? FontWeight.w600 : FontWeight.w500,
                ),
                child: Text('$likesCount'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء زر تفاعل
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    required ThemeProvider themeProvider,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(12)),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 16,
              color:
                  themeProvider.isDarkMode
                      ? const Color(0xFF9CA3AF)
                      : const Color(0xFF6B7280),
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: GoogleFonts.cairo(
                fontSize: 12,
                color:
                    themeProvider.isDarkMode
                        ? const Color(0xFF9CA3AF)
                        : const Color(0xFF6B7280),
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء شريط إدخال التعليق
  Widget _buildCommentInput(ThemeProvider themeProvider) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color:
            themeProvider.isDarkMode ? const Color(0xFF2D2D2D) : Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // شريط الرد (إذا كان في وضع الرد)
          if (_isReplyMode) ...[
            Container(
              padding: const EdgeInsets.all(12),
              margin: const EdgeInsets.only(bottom: 12),
              decoration: BoxDecoration(
                color:
                    themeProvider.isDarkMode
                        ? const Color(0xFF374151)
                        : const Color(0xFFF3F4F6),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.reply,
                    size: 16,
                    color:
                        themeProvider.isDarkMode
                            ? const Color(0xFF9CA3AF)
                            : const Color(0xFF6B7280),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'الرد على ${_replyingToComment?.authorName ?? ''}',
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color:
                            themeProvider.isDarkMode
                                ? const Color(0xFF9CA3AF)
                                : const Color(0xFF6B7280),
                      ),
                    ),
                  ),
                  GestureDetector(
                    onTap: _cancelReply,
                    child: Icon(
                      Icons.close,
                      size: 16,
                      color:
                          themeProvider.isDarkMode
                              ? const Color(0xFF9CA3AF)
                              : const Color(0xFF6B7280),
                    ),
                  ),
                ],
              ),
            ),
          ],

          // حقل إدخال التعليق
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _commentController,
                  focusNode: _commentFocusNode,
                  maxLines: null,
                  textDirection: TextDirection.rtl,
                  decoration: InputDecoration(
                    hintText: _isReplyMode ? 'اكتب ردك...' : 'اكتب تعليقك...',
                    hintStyle: GoogleFonts.cairo(
                      color:
                          themeProvider.isDarkMode
                              ? Colors.grey[400]
                              : Colors.grey[600],
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24),
                      borderSide: BorderSide(
                        color:
                            themeProvider.isDarkMode
                                ? const Color(0xFF4B5563)
                                : const Color(0xFFE5E7EB),
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24),
                      borderSide: BorderSide(
                        color:
                            themeProvider.isDarkMode
                                ? const Color(0xFF4B5563)
                                : const Color(0xFFE5E7EB),
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24),
                      borderSide: const BorderSide(
                        color: Color(0xFF6366F1),
                        width: 2,
                      ),
                    ),
                    filled: true,
                    fillColor:
                        themeProvider.isDarkMode
                            ? const Color(0xFF374151)
                            : const Color(0xFFF9FAFB),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                  style: GoogleFonts.cairo(
                    color:
                        themeProvider.isDarkMode ? Colors.white : Colors.black,
                  ),
                ),
              ),
              const SizedBox(width: 8),

              // زر الإرسال
              GestureDetector(
                onTap: _isSubmitting ? null : _addComment,
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    gradient:
                        _isSubmitting
                            ? null
                            : const LinearGradient(
                              colors: [Color(0xFF6366F1), Color(0xFF8B5CF6)],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                    color: _isSubmitting ? Colors.grey : null,
                    borderRadius: BorderRadius.circular(24),
                    boxShadow:
                        _isSubmitting
                            ? null
                            : [
                              BoxShadow(
                                color: const Color(
                                  0xFF6366F1,
                                ).withValues(alpha: 0.3),
                                blurRadius: 8,
                                offset: const Offset(0, 4),
                              ),
                            ],
                  ),
                  child:
                      _isSubmitting
                          ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.white,
                              ),
                            ),
                          )
                          : const Icon(
                            Icons.send_rounded,
                            color: Colors.white,
                            size: 22,
                          ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// تنسيق الوقت
  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    }
  }

  /// عرض حوار حذف التعليق
  void _showDeleteCommentDialog(CommunityComment comment) {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUserId = authProvider.firebaseUser?.uid ?? '';
    final currentUserEmail = authProvider.firebaseUser?.email ?? '';

    // التحقق من الصلاحيات
    final canDelete =
        comment.authorId == currentUserId ||
        currentUserEmail == '<EMAIL>';

    if (!canDelete) {
      _showErrorSnackBar('لا يمكنك حذف هذا التعليق');
      return;
    }

    HapticFeedback.mediumImpact();

    showDialog(
      context: context,
      builder:
          (context) => Consumer<ThemeProvider>(
            builder: (context, themeProvider, child) {
              return AlertDialog(
                backgroundColor:
                    themeProvider.isDarkMode
                        ? const Color(0xFF2D2D2D)
                        : Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
                title: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: const Color(0xFFEF4444).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.delete_outline,
                        color: Color(0xFFEF4444),
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'حذف التعليق',
                        style: GoogleFonts.cairo(
                          fontSize: 18,
                          fontWeight: FontWeight.w700,
                          color:
                              themeProvider.isDarkMode
                                  ? Colors.white
                                  : Colors.black,
                        ),
                      ),
                    ),
                  ],
                ),
                content: Text(
                  'هل أنت متأكد من حذف هذا التعليق؟ لا يمكن التراجع عن هذا الإجراء.',
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color:
                        themeProvider.isDarkMode
                            ? Colors.grey[300]
                            : Colors.grey[700],
                    height: 1.5,
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text(
                      'إلغاء',
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color:
                            themeProvider.isDarkMode
                                ? Colors.grey[400]
                                : Colors.grey[600],
                      ),
                    ),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                      _deleteComment(comment.id);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFEF4444),
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 12,
                      ),
                    ),
                    child: Text(
                      'حذف',
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
    );
  }

  /// حذف التعليق
  Future<void> _deleteComment(String commentId) async {
    try {
      final success = await CommunityService.deleteComment(commentId);
      if (success) {
        // إزالة التعليق من القائمة المحلية مع انيميشن
        setState(() {
          _comments.removeWhere((comment) => comment.id == commentId);
        });
        _showSuccessSnackBar('تم حذف التعليق بنجاح');
      } else {
        _showErrorSnackBar('فشل في حذف التعليق');
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في حذف التعليق');
    }
  }

  /// إعجاب بتعليق
  Future<void> _likeComment(String commentId) async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    if (!authProvider.isAuthenticated) {
      _showErrorSnackBar('يجب تسجيل الدخول أولاً');
      return;
    }

    try {
      // البحث عن التعليق في القائمة المحلية
      final commentIndex = _comments.indexWhere((c) => c.id == commentId);
      if (commentIndex == -1) return;

      final comment = _comments[commentIndex];
      final currentUserId = authProvider.firebaseUser?.uid ?? '';
      final isLiked = comment.likedBy.contains(currentUserId);

      // تحديث محلي فوري للاستجابة السريعة
      setState(() {
        if (isLiked) {
          _comments[commentIndex] = CommunityComment(
            id: comment.id,
            postId: comment.postId,
            authorId: comment.authorId,
            authorName: comment.authorName,
            content: comment.content,
            timestamp: comment.timestamp,
            likedBy:
                comment.likedBy.where((id) => id != currentUserId).toList(),
            parentCommentId: comment.parentCommentId,
            replyToUserId: comment.replyToUserId,
            replyToUserName: comment.replyToUserName,
            replies: comment.replies,
          );
        } else {
          _comments[commentIndex] = CommunityComment(
            id: comment.id,
            postId: comment.postId,
            authorId: comment.authorId,
            authorName: comment.authorName,
            content: comment.content,
            timestamp: comment.timestamp,
            likedBy: [...comment.likedBy, currentUserId],
            parentCommentId: comment.parentCommentId,
            replyToUserId: comment.replyToUserId,
            replyToUserName: comment.replyToUserName,
            replies: comment.replies,
          );
        }
      });

      // تحديث في Firebase
      await CommunityService.toggleCommentLike(commentId);
    } catch (e) {
      // إعادة التحديث في حالة الخطأ
      _loadComments();
      _showErrorSnackBar('خطأ في تحديث الإعجاب');
    }
  }
}
