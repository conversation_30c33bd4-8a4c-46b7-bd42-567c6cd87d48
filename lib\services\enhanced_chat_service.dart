import 'dart:async';
import 'package:firebase_database/firebase_database.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import '../models/enhanced_chat_message.dart';

/// خدمة الدردشة المحسنة مع التحميل التدريجي وإدارة الذاكرة
class EnhancedChatService {
  static final FirebaseDatabase _database = FirebaseDatabase.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  // مراجع قاعدة البيانات
  static final DatabaseReference _chatsRef = _database.ref().child('chats');
  static final DatabaseReference _chatRoomsRef = _database.ref().child(
    'chatRooms',
  );
  // المراجع المحذوفة لأنها غير مستخدمة حالياً
  // static final DatabaseReference _presenceRef = _database.ref().child('presence');
  // static final DatabaseReference _typingRef = _database.ref().child('typing');

  // إعدادات التحميل التدريجي
  static const int initialLoadCount = 10; // تحميل 10 رسائل في البداية
  static const int loadMoreCount = 20; // تحميل 20 رسالة إضافية
  static const int maxMessagesInMemory = 200; // الحد الأقصى للرسائل في الذاكرة
  static const int maxMessagesInDatabase = 200; // الحد الأقصى في قاعدة البيانات

  // cache للرسائل المحملة
  static final Map<String, List<EnhancedChatMessage>> _messagesCache = {};
  static final Map<String, StreamController<List<EnhancedChatMessage>>>
  _messageStreams = {};

  /// تهيئة الخدمة
  static Future<void> initialize() async {
    try {
      // تنظيف الـ cache عند بدء التطبيق
      _messagesCache.clear();
      _messageStreams.clear();

      if (kDebugMode) {
        print('✅ تم تهيئة خدمة الدردشة المحسنة');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تهيئة خدمة الدردشة: $e');
      }
    }
  }

  /// تحميل الرسائل الأولية (آخر 10 رسائل)
  static Future<MessageBatch> loadInitialMessages(String chatRoomId) async {
    try {
      if (kDebugMode) {
        print('📥 تحميل الرسائل الأولية للغرفة: $chatRoomId');
      }

      final messagesRef = _chatsRef.child(chatRoomId).child('messages');
      final snapshot =
          await messagesRef
              .orderByChild('timestamp')
              .limitToLast(initialLoadCount)
              .get();

      if (!snapshot.exists) {
        return MessageBatch(
          id: 'initial_${DateTime.now().millisecondsSinceEpoch}',
          messages: [],
          startTime: DateTime.now(),
          endTime: DateTime.now(),
          hasMore: false,
        );
      }

      final messages = <EnhancedChatMessage>[];
      final data = Map<dynamic, dynamic>.from(snapshot.value as Map);

      data.forEach((key, value) {
        final messageData = Map<dynamic, dynamic>.from(value);
        final message = EnhancedChatMessage.fromRealtimeDatabase(
          key,
          messageData,
        );
        messages.add(message);
      });

      // ترتيب الرسائل حسب الوقت (الأحدث أولاً)
      messages.sort((a, b) => b.timestamp.compareTo(a.timestamp));

      // حفظ في الـ cache
      _messagesCache[chatRoomId] = messages;

      // التحقق من وجود رسائل أقدم
      final hasMore = await _checkForOlderMessages(
        chatRoomId,
        messages.last.timestamp,
      );

      final batch = MessageBatch(
        id: 'initial_${DateTime.now().millisecondsSinceEpoch}',
        messages: messages,
        startTime:
            messages.isNotEmpty ? messages.last.timestamp : DateTime.now(),
        endTime:
            messages.isNotEmpty ? messages.first.timestamp : DateTime.now(),
        hasMore: hasMore,
      );

      if (kDebugMode) {
        print('✅ تم تحميل ${messages.length} رسالة أولية');
      }

      return batch;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تحميل الرسائل الأولية: $e');
      }
      return MessageBatch(
        id: 'error_${DateTime.now().millisecondsSinceEpoch}',
        messages: [],
        startTime: DateTime.now(),
        endTime: DateTime.now(),
        hasMore: false,
      );
    }
  }

  /// تحميل رسائل إضافية (20 رسالة أقدم)
  static Future<MessageBatch> loadMoreMessages(
    String chatRoomId,
    DateTime beforeTimestamp,
  ) async {
    try {
      if (kDebugMode) {
        print('📥 تحميل رسائل إضافية للغرفة: $chatRoomId');
      }

      final messagesRef = _chatsRef.child(chatRoomId).child('messages');
      final snapshot =
          await messagesRef
              .orderByChild('timestamp')
              .endBefore(beforeTimestamp.millisecondsSinceEpoch)
              .limitToLast(loadMoreCount)
              .get();

      if (!snapshot.exists) {
        return MessageBatch(
          id: 'more_${DateTime.now().millisecondsSinceEpoch}',
          messages: [],
          startTime: beforeTimestamp,
          endTime: beforeTimestamp,
          hasMore: false,
        );
      }

      final messages = <EnhancedChatMessage>[];
      final data = Map<dynamic, dynamic>.from(snapshot.value as Map);

      data.forEach((key, value) {
        final messageData = Map<dynamic, dynamic>.from(value);
        final message = EnhancedChatMessage.fromRealtimeDatabase(
          key,
          messageData,
        );
        messages.add(message);
      });

      // ترتيب الرسائل حسب الوقت (الأحدث أولاً)
      messages.sort((a, b) => b.timestamp.compareTo(a.timestamp));

      // دمج مع الـ cache الموجود
      final existingMessages = _messagesCache[chatRoomId] ?? [];
      final allMessages = [...existingMessages, ...messages];

      // إدارة الذاكرة - الاحتفاظ بآخر 200 رسالة فقط
      if (allMessages.length > maxMessagesInMemory) {
        allMessages.removeRange(maxMessagesInMemory, allMessages.length);
      }

      _messagesCache[chatRoomId] = allMessages;

      // التحقق من وجود رسائل أقدم
      final hasMore =
          messages.isNotEmpty
              ? await _checkForOlderMessages(
                chatRoomId,
                messages.last.timestamp,
              )
              : false;

      final batch = MessageBatch(
        id: 'more_${DateTime.now().millisecondsSinceEpoch}',
        messages: messages,
        startTime:
            messages.isNotEmpty ? messages.last.timestamp : beforeTimestamp,
        endTime:
            messages.isNotEmpty ? messages.first.timestamp : beforeTimestamp,
        hasMore: hasMore,
      );

      if (kDebugMode) {
        print('✅ تم تحميل ${messages.length} رسالة إضافية');
      }

      return batch;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تحميل الرسائل الإضافية: $e');
      }
      return MessageBatch(
        id: 'error_${DateTime.now().millisecondsSinceEpoch}',
        messages: [],
        startTime: beforeTimestamp,
        endTime: beforeTimestamp,
        hasMore: false,
      );
    }
  }

  /// التحقق من وجود رسائل أقدم
  static Future<bool> _checkForOlderMessages(
    String chatRoomId,
    DateTime beforeTimestamp,
  ) async {
    try {
      final messagesRef = _chatsRef.child(chatRoomId).child('messages');
      final snapshot =
          await messagesRef
              .orderByChild('timestamp')
              .endBefore(beforeTimestamp.millisecondsSinceEpoch)
              .limitToLast(1)
              .get();

      return snapshot.exists;
    } catch (e) {
      return false;
    }
  }

  /// الحصول على stream للرسائل مع التحديث الفوري
  static Stream<List<EnhancedChatMessage>> getMessagesStream(
    String chatRoomId,
  ) {
    // إنشاء stream جديد إذا لم يكن موجوداً
    if (!_messageStreams.containsKey(chatRoomId)) {
      _messageStreams[chatRoomId] =
          StreamController<List<EnhancedChatMessage>>.broadcast();
      _startListeningToNewMessages(chatRoomId);
    }

    return _messageStreams[chatRoomId]!.stream;
  }

  /// بدء الاستماع للرسائل الجديدة
  static void _startListeningToNewMessages(String chatRoomId) {
    final messagesRef = _chatsRef.child(chatRoomId).child('messages');

    // الاستماع للرسائل الجديدة فقط
    messagesRef.orderByChild('timestamp').onChildAdded.listen((event) {
      if (event.snapshot.exists) {
        final messageData = Map<dynamic, dynamic>.from(
          event.snapshot.value as Map,
        );
        final message = EnhancedChatMessage.fromRealtimeDatabase(
          event.snapshot.key!,
          messageData,
        );

        // التحقق من أن الرسالة جديدة (لم تكن موجودة في الـ cache)
        final existingMessages = _messagesCache[chatRoomId] ?? [];
        final messageExists = existingMessages.any((m) => m.id == message.id);

        if (!messageExists) {
          // إضافة الرسالة الجديدة في البداية (الأحدث)
          existingMessages.insert(0, message);

          // إدارة الذاكرة - الاحتفاظ بآخر 200 رسالة
          if (existingMessages.length > maxMessagesInMemory) {
            existingMessages.removeRange(
              maxMessagesInMemory,
              existingMessages.length,
            );
          }

          _messagesCache[chatRoomId] = existingMessages;

          // إرسال التحديث للـ stream
          if (_messageStreams.containsKey(chatRoomId)) {
            _messageStreams[chatRoomId]!.add(List.from(existingMessages));
          }

          if (kDebugMode) {
            print('📨 رسالة جديدة مستلمة: ${message.content}');
          }
        }
      }
    });

    // الاستماع لحذف الرسائل
    messagesRef.onChildRemoved.listen((event) {
      if (event.snapshot.exists) {
        final messageId = event.snapshot.key!;
        final existingMessages = _messagesCache[chatRoomId] ?? [];

        existingMessages.removeWhere((m) => m.id == messageId);
        _messagesCache[chatRoomId] = existingMessages;

        // إرسال التحديث للـ stream
        if (_messageStreams.containsKey(chatRoomId)) {
          _messageStreams[chatRoomId]!.add(List.from(existingMessages));
        }

        if (kDebugMode) {
          print('🗑️ رسالة محذوفة: $messageId');
        }
      }
    });
  }

  /// إرسال رسالة جديدة مع دعم الردود
  static Future<bool> sendMessage({
    required String chatRoomId,
    required String content,
    MessageType type = MessageType.text,
    String? replyToId,
    String? replyToMessageContent,
    String? replyToSenderName,
    MessageType? replyToMessageType,
    List<String> attachments = const [],
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      // إنشاء الرسالة
      final messageRef = _chatsRef.child(chatRoomId).child('messages').push();
      final timestamp = DateTime.now();

      final messageData = {
        'senderId': user.uid,
        'senderName': user.displayName ?? 'مستخدم',
        'senderAvatar': user.photoURL ?? '',
        'message': content,
        'content': content,
        'timestamp': timestamp.millisecondsSinceEpoch,
        'type': type.toString().split('.').last,
        'isRead': false,
        'replyToId': replyToId,
        'replyToMessageContent': replyToMessageContent,
        'replyToSenderName': replyToSenderName,
        'replyToMessageType': replyToMessageType?.toString().split('.').last,
        'attachments': attachments,
        'metadata': metadata,
        'sequenceNumber': timestamp.millisecondsSinceEpoch,
      };

      await messageRef.set(messageData);

      // تحديث آخر رسالة في الغرفة
      await _chatRoomsRef.child(chatRoomId).update({
        'lastMessage': content,
        'lastMessageTime': timestamp.millisecondsSinceEpoch,
      });

      // تنظيف الرسائل القديمة في قاعدة البيانات
      await _cleanupOldMessages(chatRoomId);

      if (kDebugMode) {
        print('✅ تم إرسال الرسالة بنجاح');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إرسال الرسالة: $e');
      }
      return false;
    }
  }

  /// تنظيف الرسائل القديمة (الاحتفاظ بآخر 200 رسالة)
  static Future<void> _cleanupOldMessages(String chatRoomId) async {
    try {
      final messagesRef = _chatsRef.child(chatRoomId).child('messages');

      // الحصول على جميع الرسائل مرتبة حسب الوقت
      final snapshot = await messagesRef.orderByChild('timestamp').get();

      if (!snapshot.exists) return;

      final data = Map<dynamic, dynamic>.from(snapshot.value as Map);
      final messageKeys = data.keys.toList();

      // إذا كان عدد الرسائل أكثر من الحد الأقصى
      if (messageKeys.length > maxMessagesInDatabase) {
        // ترتيب المفاتيح حسب الوقت
        messageKeys.sort((a, b) {
          final timestampA = data[a]['timestamp'] ?? 0;
          final timestampB = data[b]['timestamp'] ?? 0;
          return timestampA.compareTo(timestampB);
        });

        // حذف الرسائل الأقدم
        final keysToDelete = messageKeys.take(
          messageKeys.length - maxMessagesInDatabase,
        );

        for (final key in keysToDelete) {
          await messagesRef.child(key).remove();
        }

        if (kDebugMode) {
          print(
            '🗑️ تم حذف ${keysToDelete.length} رسالة قديمة من الغرفة: $chatRoomId',
          );
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تنظيف الرسائل القديمة: $e');
      }
    }
  }

  /// الحصول على الرسائل المحفوظة في الـ cache
  static List<EnhancedChatMessage> getCachedMessages(String chatRoomId) {
    return _messagesCache[chatRoomId] ?? [];
  }

  /// تنظيف الـ cache لغرفة معينة
  static void clearCacheForRoom(String chatRoomId) {
    _messagesCache.remove(chatRoomId);
    _messageStreams[chatRoomId]?.close();
    _messageStreams.remove(chatRoomId);
  }

  /// تنظيف جميع الـ cache
  static void clearAllCache() {
    _messagesCache.clear();
    for (final controller in _messageStreams.values) {
      controller.close();
    }
    _messageStreams.clear();
  }

  /// الحصول على رسالة محددة للرد عليها
  static Future<EnhancedChatMessage?> getMessageById(
    String chatRoomId,
    String messageId,
  ) async {
    try {
      // البحث في الـ cache أولاً
      final cachedMessages = _messagesCache[chatRoomId] ?? [];
      final cachedMessage =
          cachedMessages.where((m) => m.id == messageId).firstOrNull;

      if (cachedMessage != null) {
        return cachedMessage;
      }

      // البحث في قاعدة البيانات
      final messageRef = _chatsRef
          .child(chatRoomId)
          .child('messages')
          .child(messageId);
      final snapshot = await messageRef.get();

      if (!snapshot.exists) {
        return null;
      }

      final messageData = Map<dynamic, dynamic>.from(snapshot.value as Map);
      return EnhancedChatMessage.fromRealtimeDatabase(messageId, messageData);
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في الحصول على الرسالة: $e');
      }
      return null;
    }
  }

  /// إرسال رد على رسالة محددة
  static Future<bool> sendReply({
    required String chatRoomId,
    required String content,
    required EnhancedChatMessage replyToMessage,
    MessageType type = MessageType.text,
    List<String> attachments = const [],
    Map<String, dynamic>? metadata,
  }) async {
    return await sendMessage(
      chatRoomId: chatRoomId,
      content: content,
      type: type,
      replyToId: replyToMessage.id,
      replyToMessageContent: replyToMessage.content,
      replyToSenderName: replyToMessage.senderName,
      replyToMessageType: replyToMessage.type,
      attachments: attachments,
      metadata: metadata,
    );
  }

  /// البحث عن رسالة في الـ cache
  static EnhancedChatMessage? findMessageInCache(
    String chatRoomId,
    String messageId,
  ) {
    final messages = _messagesCache[chatRoomId] ?? [];
    try {
      return messages.firstWhere((m) => m.id == messageId);
    } catch (e) {
      return null;
    }
  }

  /// الحصول على عدد الردود على رسالة محددة
  static int getReplyCount(String chatRoomId, String messageId) {
    final messages = _messagesCache[chatRoomId] ?? [];
    return messages.where((m) => m.replyToId == messageId).length;
  }

  /// الحصول على جميع الردود على رسالة محددة
  static List<EnhancedChatMessage> getRepliesForMessage(
    String chatRoomId,
    String messageId,
  ) {
    final messages = _messagesCache[chatRoomId] ?? [];
    return messages.where((m) => m.replyToId == messageId).toList()
      ..sort((a, b) => a.timestamp.compareTo(b.timestamp));
  }

  /// إغلاق الخدمة
  static void dispose() {
    clearAllCache();
  }
}
