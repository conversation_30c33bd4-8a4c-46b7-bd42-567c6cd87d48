import 'package:flutter/foundation.dart';
import 'services/enhanced_chat_service.dart';
import 'utils/chat_performance_manager.dart';
import 'utils/chat_system_integration.dart';
import 'models/enhanced_chat_message.dart';

/// اختبار شامل لنظام الدردشة المحسن
class EnhancedChatSystemTester {
  
  /// تشغيل جميع الاختبارات
  static Future<void> runAllTests() async {
    if (!kDebugMode) {
      print('⚠️ الاختبارات تعمل فقط في وضع التطوير');
      return;
    }

    print('🚀 بدء اختبارات نظام الدردشة المحسن');
    print('=' * 60);

    try {
      // اختبار التهيئة
      await _testInitialization();
      
      // اختبار التحميل التدريجي
      await _testPagination();
      
      // اختبار إرسال الرسائل
      await _testMessageSending();
      
      // اختبار إدارة الذاكرة
      await _testMemoryManagement();
      
      // اختبار الأداء
      await _testPerformance();
      
      // اختبار التكامل
      await _testIntegration();
      
      print('=' * 60);
      print('✅ انتهت جميع الاختبارات بنجاح!');
      
      // طباعة تقرير شامل
      ChatSystemIntegration().printSystemReport();
      
    } catch (e) {
      print('❌ فشلت الاختبارات: $e');
    }
  }

  /// اختبار التهيئة
  static Future<void> _testInitialization() async {
    print('\n🔧 اختبار التهيئة...');
    
    try {
      // تهيئة النظام
      await ChatSystemIntegration().initialize();
      
      // التحقق من التهيئة
      final stats = ChatSystemIntegration().getSystemStats();
      
      if (stats['isInitialized'] == true) {
        print('✅ تم تهيئة النظام بنجاح');
      } else {
        throw Exception('فشل في تهيئة النظام');
      }
      
    } catch (e) {
      print('❌ خطأ في اختبار التهيئة: $e');
      rethrow;
    }
  }

  /// اختبار التحميل التدريجي
  static Future<void> _testPagination() async {
    print('\n📄 اختبار التحميل التدريجي...');
    
    try {
      final testRoomId = 'test_pagination_${DateTime.now().millisecondsSinceEpoch}';
      
      // إنشاء رسائل تجريبية
      await _createTestMessages(testRoomId, 25);
      
      // اختبار التحميل الأولي (10 رسائل)
      final initialBatch = await EnhancedChatService.loadInitialMessages(testRoomId);
      
      if (initialBatch.messages.length <= 10) {
        print('✅ التحميل الأولي يعمل بشكل صحيح (${initialBatch.messages.length} رسائل)');
      } else {
        throw Exception('التحميل الأولي يحمل أكثر من المطلوب');
      }
      
      // اختبار التحميل الإضافي
      if (initialBatch.hasMore) {
        final moreBatch = await EnhancedChatService.loadMoreMessages(
          testRoomId,
          initialBatch.messages.last.timestamp,
        );
        
        if (moreBatch.messages.length <= 20) {
          print('✅ التحميل الإضافي يعمل بشكل صحيح (${moreBatch.messages.length} رسائل)');
        } else {
          throw Exception('التحميل الإضافي يحمل أكثر من المطلوب');
        }
      }
      
    } catch (e) {
      print('❌ خطأ في اختبار التحميل التدريجي: $e');
      rethrow;
    }
  }

  /// اختبار إرسال الرسائل
  static Future<void> _testMessageSending() async {
    print('\n📤 اختبار إرسال الرسائل...');
    
    try {
      final testRoomId = 'test_sending_${DateTime.now().millisecondsSinceEpoch}';
      
      // اختبار إرسال رسالة نصية
      final textResult = await EnhancedChatService.sendMessage(
        chatRoomId: testRoomId,
        content: 'رسالة نصية تجريبية',
        type: MessageType.text,
      );
      
      if (textResult) {
        print('✅ إرسال الرسائل النصية يعمل بشكل صحيح');
      } else {
        throw Exception('فشل في إرسال الرسالة النصية');
      }
      
      // اختبار إرسال رسالة مع مرفقات
      final attachmentResult = await EnhancedChatService.sendMessage(
        chatRoomId: testRoomId,
        content: 'رسالة مع مرفق',
        type: MessageType.image,
        attachments: ['https://example.com/image.jpg'],
      );
      
      if (attachmentResult) {
        print('✅ إرسال الرسائل مع المرفقات يعمل بشكل صحيح');
      } else {
        throw Exception('فشل في إرسال الرسالة مع المرفق');
      }
      
    } catch (e) {
      print('❌ خطأ في اختبار إرسال الرسائل: $e');
      rethrow;
    }
  }

  /// اختبار إدارة الذاكرة
  static Future<void> _testMemoryManagement() async {
    print('\n🧠 اختبار إدارة الذاكرة...');
    
    try {
      final testRoomId = 'test_memory_${DateTime.now().millisecondsSinceEpoch}';
      
      // تسجيل الغرفة
      ChatPerformanceManager().registerRoom(testRoomId);
      
      // إنشاء رسائل كثيرة (أكثر من الحد المسموح)
      await _createTestMessages(testRoomId, 250);
      
      // التحقق من عدد الرسائل في الذاكرة
      final cachedMessages = EnhancedChatService.getCachedMessages(testRoomId);
      
      if (cachedMessages.length <= 200) {
        print('✅ إدارة الذاكرة تعمل بشكل صحيح (${cachedMessages.length} رسائل في الذاكرة)');
      } else {
        print('⚠️ تحذير: عدد الرسائل في الذاكرة أكثر من المتوقع (${cachedMessages.length})');
      }
      
      // تنظيف الغرفة
      ChatPerformanceManager().unregisterRoom(testRoomId);
      
    } catch (e) {
      print('❌ خطأ في اختبار إدارة الذاكرة: $e');
      rethrow;
    }
  }

  /// اختبار الأداء
  static Future<void> _testPerformance() async {
    print('\n⚡ اختبار الأداء...');
    
    try {
      final testRoomId = 'test_performance_${DateTime.now().millisecondsSinceEpoch}';
      
      // قياس وقت التحميل الأولي
      final stopwatch = Stopwatch()..start();
      
      await EnhancedChatService.loadInitialMessages(testRoomId);
      
      stopwatch.stop();
      final loadTime = stopwatch.elapsedMilliseconds;
      
      if (loadTime < 1000) { // أقل من ثانية واحدة
        print('✅ الأداء ممتاز - وقت التحميل: ${loadTime}ms');
      } else if (loadTime < 3000) { // أقل من 3 ثوان
        print('⚠️ الأداء مقبول - وقت التحميل: ${loadTime}ms');
      } else {
        print('❌ الأداء بطيء - وقت التحميل: ${loadTime}ms');
      }
      
      // اختبار إرسال رسائل متعددة
      stopwatch.reset();
      stopwatch.start();
      
      for (int i = 0; i < 10; i++) {
        await EnhancedChatService.sendMessage(
          chatRoomId: testRoomId,
          content: 'رسالة أداء $i',
        );
      }
      
      stopwatch.stop();
      final sendTime = stopwatch.elapsedMilliseconds;
      
      print('📊 وقت إرسال 10 رسائل: ${sendTime}ms (${sendTime / 10}ms لكل رسالة)');
      
    } catch (e) {
      print('❌ خطأ في اختبار الأداء: $e');
      rethrow;
    }
  }

  /// اختبار التكامل
  static Future<void> _testIntegration() async {
    print('\n🔗 اختبار التكامل...');
    
    try {
      // اختبار النظام المدمج
      final systemTest = await ChatSystemIntegration().runSystemTest();
      
      if (systemTest) {
        print('✅ اختبار التكامل نجح');
      } else {
        throw Exception('فشل اختبار التكامل');
      }
      
      // اختبار الإحصائيات
      final stats = ChatSystemIntegration().getSystemStats();
      
      if (stats.isNotEmpty) {
        print('✅ الإحصائيات تعمل بشكل صحيح');
        print('   • النظام المحسن: ${stats['enhancedModeEnabled']}');
        print('   • الإصدار: ${stats['systemVersion']}');
      } else {
        throw Exception('فشل في الحصول على الإحصائيات');
      }
      
    } catch (e) {
      print('❌ خطأ في اختبار التكامل: $e');
      rethrow;
    }
  }

  /// إنشاء رسائل تجريبية
  static Future<void> _createTestMessages(String roomId, int count) async {
    for (int i = 0; i < count; i++) {
      await EnhancedChatService.sendMessage(
        chatRoomId: roomId,
        content: 'رسالة تجريبية رقم ${i + 1}',
        type: MessageType.text,
      );
      
      // تأخير قصير لمحاكاة الاستخدام الحقيقي
      await Future.delayed(const Duration(milliseconds: 10));
    }
  }

  /// اختبار سريع للتطوير
  static Future<void> quickTest() async {
    if (!kDebugMode) return;
    
    print('🏃‍♂️ اختبار سريع...');
    
    try {
      await ChatSystemIntegration().initialize();
      
      final testRoomId = 'quick_test_${DateTime.now().millisecondsSinceEpoch}';
      
      // إرسال رسالة واحدة
      final result = await EnhancedChatService.sendMessage(
        chatRoomId: testRoomId,
        content: 'اختبار سريع',
      );
      
      if (result) {
        print('✅ الاختبار السريع نجح');
      } else {
        print('❌ الاختبار السريع فشل');
      }
      
    } catch (e) {
      print('❌ خطأ في الاختبار السريع: $e');
    }
  }
}

/// دالة مساعدة لتشغيل الاختبارات من أي مكان
Future<void> runEnhancedChatTests() async {
  await EnhancedChatSystemTester.runAllTests();
}

/// دالة مساعدة للاختبار السريع
Future<void> runQuickChatTest() async {
  await EnhancedChatSystemTester.quickTest();
}
