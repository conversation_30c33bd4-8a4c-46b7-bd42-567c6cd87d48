import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';

import '../providers/theme_provider.dart';
import '../services/download_manager.dart';
import '../models/downloaded_pdf.dart';
import '../models/subject.dart';
import '../models/pdf_model.dart';
import 'pdf_viewer_screen.dart';

class DownloadsScreen extends StatefulWidget {
  const DownloadsScreen({super.key});

  @override
  State<DownloadsScreen> createState() => _DownloadsScreenState();
}

class _DownloadsScreenState extends State<DownloadsScreen> {
  List<DownloadedPDF> _downloadedFiles = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadDownloadedFiles();
  }

  Future<void> _loadDownloadedFiles() async {
    try {
      final files = await DownloadManager.getDownloadedFiles();
      setState(() {
        _downloadedFiles = files;
        _isLoading = false;
      });
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في تحميل الملفات المحملة: $e');
      }
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _deleteFile(DownloadedPDF file) async {
    final bool? shouldDelete = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return Consumer<ThemeProvider>(
          builder: (context, themeProvider, child) {
            return AlertDialog(
              backgroundColor:
                  themeProvider.isDarkMode
                      ? const Color(0xFF1E293B)
                      : Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              title: Text(
                'حذف الملف',
                style: GoogleFonts.cairo(
                  fontWeight: FontWeight.bold,
                  color:
                      themeProvider.isDarkMode
                          ? const Color(0xFFF1F5F9)
                          : const Color(0xFF1F2937),
                ),
              ),
              content: Text(
                'هل أنت متأكد من حذف "${file.name}"؟',
                style: GoogleFonts.cairo(
                  color:
                      themeProvider.isDarkMode
                          ? const Color(0xFFCBD5E1)
                          : const Color(0xFF6B7280),
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: Text(
                    'إلغاء',
                    style: GoogleFonts.cairo(
                      color:
                          themeProvider.isDarkMode
                              ? const Color(0xFF94A3B8)
                              : const Color(0xFF6B7280),
                    ),
                  ),
                ),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  child: Text(
                    'حذف',
                    style: GoogleFonts.cairo(color: Colors.red),
                  ),
                ),
              ],
            );
          },
        );
      },
    );

    if (shouldDelete == true) {
      try {
        await DownloadManager.deleteFile(file);
        await _loadDownloadedFiles();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم حذف الملف بنجاح', style: GoogleFonts.cairo()),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في حذف الملف: $e', style: GoogleFonts.cairo()),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  void _openFile(DownloadedPDF file) {
    // إنشاء Subject مؤقت للملف المحمل
    final tempSubject = Subject(
      id: file.subject,
      name: file.subject,
      arabicName: file.subject,
      credits: 3,
      pdfFiles: [],
      description: 'ملف محمل',
    );

    // إنشاء PDFModel مؤقت للملف المحمل
    final tempPDFModel = PDFModel(
      id: file.id,
      name: file.name,
      url: file.localPath, // استخدام المسار المحلي
      category: file.category,
      subjectId: file.subject,
      subjectName: file.subject,
      yearId: '2024',
      semesterId: '1',
      createdAt: file.downloadDate,
      updatedAt: file.downloadDate,
      uploadedBy: 'system',
      uploaderName: 'النظام',
      fileName: file.name,
      isFromUrl: false,
      originalUrl: file.originalUrl,
    );

    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => PDFViewerScreen(
              subject: tempSubject,
              pdfModel: tempPDFModel,
              category: file.category,
            ),
      ),
    );
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Scaffold(
          backgroundColor:
              themeProvider.isDarkMode
                  ? const Color(0xFF0F172A)
                  : const Color(0xFFF8FAFC),
          appBar: AppBar(
            backgroundColor: Colors.transparent,
            elevation: 0,
            leading: IconButton(
              onPressed: () => Navigator.pop(context),
              icon: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color:
                      themeProvider.isDarkMode
                          ? const Color(0xFF1E293B)
                          : Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Icon(
                  Icons.arrow_back_ios_new,
                  color:
                      themeProvider.isDarkMode
                          ? const Color(0xFFF1F5F9)
                          : const Color(0xFF1F2937),
                  size: 20,
                ),
              ),
            ),
            title: Text(
              'التحميلات',
              style: GoogleFonts.cairo(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color:
                    themeProvider.isDarkMode
                        ? const Color(0xFFF1F5F9)
                        : const Color(0xFF1F2937),
              ),
            ),
            centerTitle: true,
          ),
          body:
              _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _downloadedFiles.isEmpty
                  ? _buildEmptyState(themeProvider)
                  : _buildFilesList(themeProvider),
        );
      },
    );
  }

  Widget _buildEmptyState(ThemeProvider themeProvider) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color:
                  themeProvider.isDarkMode
                      ? const Color(0xFF1E293B)
                      : Colors.white,
              borderRadius: BorderRadius.circular(60),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Icon(
              Icons.download_outlined,
              size: 60,
              color:
                  themeProvider.isDarkMode
                      ? const Color(0xFF64748B)
                      : const Color(0xFF9CA3AF),
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'لا توجد ملفات محملة',
            style: GoogleFonts.cairo(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color:
                  themeProvider.isDarkMode
                      ? const Color(0xFFF1F5F9)
                      : const Color(0xFF1F2937),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'قم بتحميل ملفات PDF لتظهر هنا',
            style: GoogleFonts.cairo(
              fontSize: 16,
              color:
                  themeProvider.isDarkMode
                      ? const Color(0xFF64748B)
                      : const Color(0xFF6B7280),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilesList(ThemeProvider themeProvider) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _downloadedFiles.length,
      itemBuilder: (context, index) {
        final file = _downloadedFiles[index];
        return _buildFileItem(file, themeProvider);
      },
    );
  }

  Widget _buildFileItem(DownloadedPDF file, ThemeProvider themeProvider) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color:
            themeProvider.isDarkMode ? const Color(0xFF1E293B) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _openFile(file),
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // PDF Icon
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [Color(0xFF6366F1), Color(0xFF8B5CF6)],
                    ),
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFF6366F1).withValues(alpha: 0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.picture_as_pdf_rounded,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                // File Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        file.name,
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color:
                              themeProvider.isDarkMode
                                  ? const Color(0xFFF1F5F9)
                                  : const Color(0xFF1F2937),
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(
                            Icons.folder_outlined,
                            size: 14,
                            color:
                                themeProvider.isDarkMode
                                    ? const Color(0xFF64748B)
                                    : const Color(0xFF6B7280),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            file.subject,
                            style: GoogleFonts.cairo(
                              fontSize: 12,
                              color:
                                  themeProvider.isDarkMode
                                      ? const Color(0xFF64748B)
                                      : const Color(0xFF6B7280),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Icon(
                            Icons.category_outlined,
                            size: 14,
                            color:
                                themeProvider.isDarkMode
                                    ? const Color(0xFF64748B)
                                    : const Color(0xFF6B7280),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            file.category,
                            style: GoogleFonts.cairo(
                              fontSize: 12,
                              color:
                                  themeProvider.isDarkMode
                                      ? const Color(0xFF64748B)
                                      : const Color(0xFF6B7280),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(
                            Icons.storage_outlined,
                            size: 14,
                            color:
                                themeProvider.isDarkMode
                                    ? const Color(0xFF64748B)
                                    : const Color(0xFF6B7280),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            _formatFileSize(file.size),
                            style: GoogleFonts.cairo(
                              fontSize: 12,
                              color:
                                  themeProvider.isDarkMode
                                      ? const Color(0xFF64748B)
                                      : const Color(0xFF6B7280),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Icon(
                            Icons.access_time,
                            size: 14,
                            color:
                                themeProvider.isDarkMode
                                    ? const Color(0xFF64748B)
                                    : const Color(0xFF6B7280),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            _formatDate(file.downloadDate),
                            style: GoogleFonts.cairo(
                              fontSize: 12,
                              color:
                                  themeProvider.isDarkMode
                                      ? const Color(0xFF64748B)
                                      : const Color(0xFF6B7280),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                // Actions
                PopupMenuButton<String>(
                  icon: Icon(
                    Icons.more_vert,
                    color:
                        themeProvider.isDarkMode
                            ? const Color(0xFF64748B)
                            : const Color(0xFF6B7280),
                  ),
                  onSelected: (value) {
                    if (value == 'delete') {
                      _deleteFile(file);
                    }
                  },
                  itemBuilder:
                      (context) => [
                        PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              const Icon(
                                Icons.delete,
                                color: Colors.red,
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'حذف',
                                style: GoogleFonts.cairo(color: Colors.red),
                              ),
                            ],
                          ),
                        ),
                      ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
