import 'package:hive/hive.dart';

part 'user_data.g.dart';

@HiveType(typeId: 2)
class UserData extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String email;

  @HiveField(2)
  final String name;

  @HiveField(3)
  final String? profileImageUrl;

  @HiveField(4)
  final String? phoneNumber;

  @HiveField(5)
  final DateTime createdAt;

  @HiveField(6)
  final DateTime lastLoginAt;

  @HiveField(7)
  final bool isEmailVerified;

  @HiveField(8)
  final String? academicYear;

  @HiveField(9)
  final List<String> favoriteSubjects;

  @HiveField(10)
  final Map<String, dynamic> preferences;

  @HiveField(11)
  final bool isOnline;

  @HiveField(12)
  final DateTime? lastSeenAt;

  @HiveField(13)
  final String? fcmToken;

  @HiveField(14)
  final bool notificationsEnabled;

  @HiveField(15)
  final String? localProfileImagePath;

  @HiveField(16)
  final int totalMessagesCount;

  @HiveField(17)
  final int totalPdfsDownloaded;

  @HiveField(18)
  final DateTime? lastSyncAt;

  UserData({
    required this.id,
    required this.email,
    required this.name,
    this.profileImageUrl,
    this.phoneNumber,
    required this.createdAt,
    required this.lastLoginAt,
    this.isEmailVerified = false,
    this.academicYear,
    this.favoriteSubjects = const [],
    this.preferences = const {},
    this.isOnline = false,
    this.lastSeenAt,
    this.fcmToken,
    this.notificationsEnabled = true,
    this.localProfileImagePath,
    this.totalMessagesCount = 0,
    this.totalPdfsDownloaded = 0,
    this.lastSyncAt,
  });

  /// إنشاء نسخة محدثة من بيانات المستخدم
  UserData copyWith({
    String? id,
    String? email,
    String? name,
    String? profileImageUrl,
    String? phoneNumber,
    DateTime? createdAt,
    DateTime? lastLoginAt,
    bool? isEmailVerified,
    String? academicYear,
    List<String>? favoriteSubjects,
    Map<String, dynamic>? preferences,
    bool? isOnline,
    DateTime? lastSeenAt,
    String? fcmToken,
    bool? notificationsEnabled,
    String? localProfileImagePath,
    int? totalMessagesCount,
    int? totalPdfsDownloaded,
    DateTime? lastSyncAt,
  }) {
    return UserData(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      academicYear: academicYear ?? this.academicYear,
      favoriteSubjects: favoriteSubjects ?? this.favoriteSubjects,
      preferences: preferences ?? this.preferences,
      isOnline: isOnline ?? this.isOnline,
      lastSeenAt: lastSeenAt ?? this.lastSeenAt,
      fcmToken: fcmToken ?? this.fcmToken,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      localProfileImagePath: localProfileImagePath ?? this.localProfileImagePath,
      totalMessagesCount: totalMessagesCount ?? this.totalMessagesCount,
      totalPdfsDownloaded: totalPdfsDownloaded ?? this.totalPdfsDownloaded,
      lastSyncAt: lastSyncAt ?? this.lastSyncAt,
    );
  }

  /// تحويل إلى Map للإرسال إلى Firebase
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'email': email,
      'name': name,
      'profileImageUrl': profileImageUrl,
      'phoneNumber': phoneNumber,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'lastLoginAt': lastLoginAt.millisecondsSinceEpoch,
      'isEmailVerified': isEmailVerified,
      'academicYear': academicYear,
      'favoriteSubjects': favoriteSubjects,
      'preferences': preferences,
      'isOnline': isOnline,
      'lastSeenAt': lastSeenAt?.millisecondsSinceEpoch,
      'fcmToken': fcmToken,
      'notificationsEnabled': notificationsEnabled,
      'totalMessagesCount': totalMessagesCount,
      'totalPdfsDownloaded': totalPdfsDownloaded,
    };
  }

  /// إنشاء من Map (من Firebase)
  factory UserData.fromMap(Map<String, dynamic> map) {
    return UserData(
      id: map['id'] ?? '',
      email: map['email'] ?? '',
      name: map['name'] ?? '',
      profileImageUrl: map['profileImageUrl'],
      phoneNumber: map['phoneNumber'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
      lastLoginAt: DateTime.fromMillisecondsSinceEpoch(map['lastLoginAt'] ?? 0),
      isEmailVerified: map['isEmailVerified'] ?? false,
      academicYear: map['academicYear'],
      favoriteSubjects: List<String>.from(map['favoriteSubjects'] ?? []),
      preferences: Map<String, dynamic>.from(map['preferences'] ?? {}),
      isOnline: map['isOnline'] ?? false,
      lastSeenAt: map['lastSeenAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['lastSeenAt'])
          : null,
      fcmToken: map['fcmToken'],
      notificationsEnabled: map['notificationsEnabled'] ?? true,
      totalMessagesCount: map['totalMessagesCount'] ?? 0,
      totalPdfsDownloaded: map['totalPdfsDownloaded'] ?? 0,
      lastSyncAt: DateTime.now(),
    );
  }

  /// الحصول على الاسم المختصر (الأحرف الأولى)
  String get initials {
    final words = name.trim().split(' ');
    if (words.isEmpty) return '؟';
    if (words.length == 1) return words[0][0].toUpperCase();
    return '${words[0][0]}${words[1][0]}'.toUpperCase();
  }

  /// الحصول على السنة الأكاديمية بالعربية
  String get academicYearName {
    switch (academicYear) {
      case 'first':
        return 'السنة الأولى';
      case 'second':
        return 'السنة الثانية';
      case 'third':
        return 'السنة الثالثة';
      case 'fourth':
        return 'السنة الرابعة';
      default:
        return academicYear ?? 'غير محدد';
    }
  }

  /// التحقق من كون المستخدم نشط حالياً
  bool get isActiveNow {
    if (!isOnline) return false;
    if (lastSeenAt == null) return false;
    return DateTime.now().difference(lastSeenAt!).inMinutes < 5;
  }

  /// الحصول على حالة آخر ظهور
  String get lastSeenStatus {
    if (isOnline && isActiveNow) return 'متصل الآن';
    if (lastSeenAt == null) return 'غير معروف';
    
    final difference = DateTime.now().difference(lastSeenAt!);
    
    if (difference.inMinutes < 1) {
      return 'منذ لحظات';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return 'منذ أكثر من أسبوع';
    }
  }

  /// إضافة موضوع للمفضلة
  UserData addFavoriteSubject(String subject) {
    if (favoriteSubjects.contains(subject)) return this;
    return copyWith(favoriteSubjects: [...favoriteSubjects, subject]);
  }

  /// إزالة موضوع من المفضلة
  UserData removeFavoriteSubject(String subject) {
    return copyWith(
      favoriteSubjects: favoriteSubjects.where((s) => s != subject).toList(),
    );
  }

  /// تحديث تفضيل
  UserData updatePreference(String key, dynamic value) {
    final newPreferences = Map<String, dynamic>.from(preferences);
    newPreferences[key] = value;
    return copyWith(preferences: newPreferences);
  }

  /// تحديث إحصائية الرسائل
  UserData incrementMessageCount() {
    return copyWith(totalMessagesCount: totalMessagesCount + 1);
  }

  /// تحديث إحصائية ملفات PDF
  UserData incrementPdfCount() {
    return copyWith(totalPdfsDownloaded: totalPdfsDownloaded + 1);
  }

  /// تحديث حالة الاتصال
  UserData updateOnlineStatus(bool online) {
    return copyWith(
      isOnline: online,
      lastSeenAt: online ? DateTime.now() : lastSeenAt,
    );
  }

  /// تحديث آخر مزامنة
  UserData updateLastSync() {
    return copyWith(lastSyncAt: DateTime.now());
  }

  @override
  String toString() {
    return 'UserData(id: $id, name: $name, email: $email, academicYear: $academicYearName)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserData && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// محول Hive لبيانات المستخدم
class UserDataAdapter extends TypeAdapter<UserData> {
  @override
  final int typeId = 2;

  @override
  UserData read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return UserData(
      id: fields[0] as String,
      email: fields[1] as String,
      name: fields[2] as String,
      profileImageUrl: fields[3] as String?,
      phoneNumber: fields[4] as String?,
      createdAt: fields[5] as DateTime,
      lastLoginAt: fields[6] as DateTime,
      isEmailVerified: fields[7] as bool,
      academicYear: fields[8] as String?,
      favoriteSubjects: (fields[9] as List?)?.cast<String>() ?? [],
      preferences: Map<String, dynamic>.from(fields[10] as Map? ?? {}),
      isOnline: fields[11] as bool,
      lastSeenAt: fields[12] as DateTime?,
      fcmToken: fields[13] as String?,
      notificationsEnabled: fields[14] as bool,
      localProfileImagePath: fields[15] as String?,
      totalMessagesCount: fields[16] as int,
      totalPdfsDownloaded: fields[17] as int,
      lastSyncAt: fields[18] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, UserData obj) {
    writer
      ..writeByte(19)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.email)
      ..writeByte(2)
      ..write(obj.name)
      ..writeByte(3)
      ..write(obj.profileImageUrl)
      ..writeByte(4)
      ..write(obj.phoneNumber)
      ..writeByte(5)
      ..write(obj.createdAt)
      ..writeByte(6)
      ..write(obj.lastLoginAt)
      ..writeByte(7)
      ..write(obj.isEmailVerified)
      ..writeByte(8)
      ..write(obj.academicYear)
      ..writeByte(9)
      ..write(obj.favoriteSubjects)
      ..writeByte(10)
      ..write(obj.preferences)
      ..writeByte(11)
      ..write(obj.isOnline)
      ..writeByte(12)
      ..write(obj.lastSeenAt)
      ..writeByte(13)
      ..write(obj.fcmToken)
      ..writeByte(14)
      ..write(obj.notificationsEnabled)
      ..writeByte(15)
      ..write(obj.localProfileImagePath)
      ..writeByte(16)
      ..write(obj.totalMessagesCount)
      ..writeByte(17)
      ..write(obj.totalPdfsDownloaded)
      ..writeByte(18)
      ..write(obj.lastSyncAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserDataAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
