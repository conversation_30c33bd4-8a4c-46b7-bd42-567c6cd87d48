// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_data.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class UserDataAdapter extends TypeAdapter<UserData> {
  @override
  final int typeId = 3;

  @override
  UserData read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return UserData(
      id: fields[0] as String,
      email: fields[1] as String,
      name: fields[2] as String,
      profileImageUrl: fields[3] as String?,
      phoneNumber: fields[4] as String?,
      createdAt: fields[5] as DateTime,
      lastLoginAt: fields[6] as DateTime,
      isEmailVerified: fields[7] as bool,
      academicYear: fields[8] as String?,
      favoriteSubjects: (fields[9] as List?)?.cast<String>() ?? [],
      preferences: Map<String, dynamic>.from(fields[10] as Map? ?? {}),
      isOnline: fields[11] as bool,
      lastSeenAt: fields[12] as DateTime?,
      fcmToken: fields[13] as String?,
      notificationsEnabled: fields[14] as bool,
      localProfileImagePath: fields[15] as String?,
      totalMessagesCount: fields[16] as int,
      totalPdfsDownloaded: fields[17] as int,
      lastSyncAt: fields[18] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, UserData obj) {
    writer
      ..writeByte(19)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.email)
      ..writeByte(2)
      ..write(obj.name)
      ..writeByte(3)
      ..write(obj.profileImageUrl)
      ..writeByte(4)
      ..write(obj.phoneNumber)
      ..writeByte(5)
      ..write(obj.createdAt)
      ..writeByte(6)
      ..write(obj.lastLoginAt)
      ..writeByte(7)
      ..write(obj.isEmailVerified)
      ..writeByte(8)
      ..write(obj.academicYear)
      ..writeByte(9)
      ..write(obj.favoriteSubjects)
      ..writeByte(10)
      ..write(obj.preferences)
      ..writeByte(11)
      ..write(obj.isOnline)
      ..writeByte(12)
      ..write(obj.lastSeenAt)
      ..writeByte(13)
      ..write(obj.fcmToken)
      ..writeByte(14)
      ..write(obj.notificationsEnabled)
      ..writeByte(15)
      ..write(obj.localProfileImagePath)
      ..writeByte(16)
      ..write(obj.totalMessagesCount)
      ..writeByte(17)
      ..write(obj.totalPdfsDownloaded)
      ..writeByte(18)
      ..write(obj.lastSyncAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserDataAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
