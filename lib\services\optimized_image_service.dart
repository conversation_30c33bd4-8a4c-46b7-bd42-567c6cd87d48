import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:http/http.dart' as http;
import 'package:crypto/crypto.dart';
import 'dart:convert';

/// خدمة محسنة لتحميل وعرض الصور مع caching
class OptimizedImageService {
  static final Map<String, Uint8List> _memoryCache = {};
  static final Map<String, String> _diskCacheMap = {};
  static const int _maxMemoryCacheSize = 50; // عدد الصور في الذاكرة
  static const Duration _cacheExpiry = Duration(days: 7);
  
  /// تحميل صورة مع caching
  static Future<Uint8List?> loadImage(String url) async {
    try {
      // التحقق من cache الذاكرة أولاً
      if (_memoryCache.containsKey(url)) {
        return _memoryCache[url];
      }
      
      // التحقق من cache القرص
      final cachedFile = await _getCachedFile(url);
      if (cachedFile != null && await cachedFile.exists()) {
        final bytes = await cachedFile.readAsBytes();
        _addToMemoryCache(url, bytes);
        return bytes;
      }
      
      // تحميل من الإنترنت
      final response = await http.get(Uri.parse(url));
      if (response.statusCode == 200) {
        final bytes = response.bodyBytes;
        
        // حفظ في cache
        await _saveToDiskCache(url, bytes);
        _addToMemoryCache(url, bytes);
        
        return bytes;
      }
      
      return null;
    } catch (e) {
      print('خطأ في تحميل الصورة: $e');
      return null;
    }
  }
  
  /// إضافة صورة إلى cache الذاكرة
  static void _addToMemoryCache(String url, Uint8List bytes) {
    // إزالة الصور القديمة إذا تجاوز الحد المسموح
    if (_memoryCache.length >= _maxMemoryCacheSize) {
      final firstKey = _memoryCache.keys.first;
      _memoryCache.remove(firstKey);
    }
    
    _memoryCache[url] = bytes;
  }
  
  /// الحصول على ملف cache من القرص
  static Future<File?> _getCachedFile(String url) async {
    try {
      final cacheDir = await _getCacheDirectory();
      final fileName = _generateFileName(url);
      final file = File('${cacheDir.path}/$fileName');
      
      // التحقق من انتهاء صلاحية الملف
      if (await file.exists()) {
        final stat = await file.stat();
        final age = DateTime.now().difference(stat.modified);
        if (age > _cacheExpiry) {
          await file.delete();
          return null;
        }
      }
      
      return file;
    } catch (e) {
      return null;
    }
  }
  
  /// حفظ صورة في cache القرص
  static Future<void> _saveToDiskCache(String url, Uint8List bytes) async {
    try {
      final cacheDir = await _getCacheDirectory();
      final fileName = _generateFileName(url);
      final file = File('${cacheDir.path}/$fileName');
      
      await file.writeAsBytes(bytes);
      _diskCacheMap[url] = file.path;
    } catch (e) {
      print('خطأ في حفظ الصورة في cache: $e');
    }
  }
  
  /// الحصول على مجلد cache
  static Future<Directory> _getCacheDirectory() async {
    final tempDir = await getTemporaryDirectory();
    final cacheDir = Directory('${tempDir.path}/image_cache');
    
    if (!await cacheDir.exists()) {
      await cacheDir.create(recursive: true);
    }
    
    return cacheDir;
  }
  
  /// توليد اسم ملف فريد للصورة
  static String _generateFileName(String url) {
    final bytes = utf8.encode(url);
    final digest = sha256.convert(bytes);
    return '${digest.toString()}.jpg';
  }
  
  /// تنظيف cache
  static Future<void> clearCache() async {
    try {
      _memoryCache.clear();
      _diskCacheMap.clear();
      
      final cacheDir = await _getCacheDirectory();
      if (await cacheDir.exists()) {
        await cacheDir.delete(recursive: true);
      }
    } catch (e) {
      print('خطأ في تنظيف cache: $e');
    }
  }
  
  /// الحصول على حجم cache
  static Future<int> getCacheSize() async {
    try {
      final cacheDir = await _getCacheDirectory();
      if (!await cacheDir.exists()) return 0;
      
      int totalSize = 0;
      await for (final entity in cacheDir.list(recursive: true)) {
        if (entity is File) {
          final stat = await entity.stat();
          totalSize += stat.size;
        }
      }
      
      return totalSize;
    } catch (e) {
      return 0;
    }
  }
  
  /// تنظيف cache القديم
  static Future<void> cleanupOldCache() async {
    try {
      final cacheDir = await _getCacheDirectory();
      if (!await cacheDir.exists()) return;
      
      final now = DateTime.now();
      await for (final entity in cacheDir.list()) {
        if (entity is File) {
          final stat = await entity.stat();
          final age = now.difference(stat.modified);
          if (age > _cacheExpiry) {
            await entity.delete();
          }
        }
      }
    } catch (e) {
      print('خطأ في تنظيف cache القديم: $e');
    }
  }
}

/// Widget محسن لعرض الصور مع caching
class OptimizedNetworkImage extends StatefulWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final Widget? placeholder;
  final Widget? errorWidget;
  
  const OptimizedNetworkImage({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.errorWidget,
  });
  
  @override
  State<OptimizedNetworkImage> createState() => _OptimizedNetworkImageState();
}

class _OptimizedNetworkImageState extends State<OptimizedNetworkImage> {
  Uint8List? _imageBytes;
  bool _isLoading = true;
  bool _hasError = false;
  
  @override
  void initState() {
    super.initState();
    _loadImage();
  }
  
  Future<void> _loadImage() async {
    try {
      final bytes = await OptimizedImageService.loadImage(widget.imageUrl);
      if (mounted) {
        setState(() {
          _imageBytes = bytes;
          _isLoading = false;
          _hasError = bytes == null;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = true;
        });
      }
    }
  }
  
  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return widget.placeholder ?? 
        Container(
          width: widget.width,
          height: widget.height,
          color: Colors.grey[300],
          child: const Center(
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
        );
    }
    
    if (_hasError || _imageBytes == null) {
      return widget.errorWidget ?? 
        Container(
          width: widget.width,
          height: widget.height,
          color: Colors.grey[300],
          child: const Icon(Icons.error, color: Colors.grey),
        );
    }
    
    return Image.memory(
      _imageBytes!,
      width: widget.width,
      height: widget.height,
      fit: widget.fit,
    );
  }
}
