class DownloadedPDF {
  final String id;
  final String name;
  final String subject;
  final String category;
  final String localPath;
  final String originalUrl;
  final int size;
  final DateTime downloadDate;

  DownloadedPDF({
    required this.id,
    required this.name,
    required this.subject,
    required this.category,
    required this.localPath,
    required this.originalUrl,
    required this.size,
    required this.downloadDate,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'subject': subject,
      'category': category,
      'localPath': localPath,
      'originalUrl': originalUrl,
      'size': size,
      'downloadDate': downloadDate.millisecondsSinceEpoch,
    };
  }

  factory DownloadedPDF.fromJson(Map<String, dynamic> json) {
    return DownloadedPDF(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      subject: json['subject'] ?? '',
      category: json['category'] ?? '',
      localPath: json['localPath'] ?? '',
      originalUrl: json['originalUrl'] ?? '',
      size: json['size'] ?? 0,
      downloadDate: DateTime.fromMillisecondsSinceEpoch(
        json['downloadDate'] ?? DateTime.now().millisecondsSinceEpoch,
      ),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DownloadedPDF && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
