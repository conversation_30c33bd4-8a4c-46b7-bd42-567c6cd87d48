// قواعد Firestore للإشعارات
// يجب إضافة هذه القواعد في Firebase Console > Firestore Database > Rules

rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // قواعد الإشعارات
    match /notifications/{notificationId} {
      // السماح بالقراءة للمستخدم المالك للإشعار فقط
      allow read: if request.auth != null && 
                     request.auth.uid == resource.data.userId;
      
      // السماح بالكتابة للمستخدمين المصادق عليهم (للنظام)
      allow write: if request.auth != null;
      
      // السماح بالحذف للمستخدم المالك أو الأدمن
      allow delete: if request.auth != null && 
                       (request.auth.uid == resource.data.userId ||
                        request.auth.token.email == '<EMAIL>');
    }
    
    // قواعد المستخدمين (مطلوبة للإشعارات)
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      allow read: if request.auth != null; // للنظام لقراءة بيانات المستخدمين
    }
    
    // قواعد عامة أخرى...
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}

/*
تعليمات التطبيق:

1. انتقل إلى Firebase Console
2. اختر مشروعك
3. انتقل إلى Firestore Database
4. اضغط على تبويب "Rules"
5. انسخ والصق القواعد أعلاه
6. اضغط "Publish"

هذه القواعد تسمح بـ:
- قراءة الإشعارات للمستخدم المالك فقط
- كتابة الإشعارات للمستخدمين المصادق عليهم
- حذف الإشعارات للمالك أو الأدمن
- قراءة بيانات المستخدمين للنظام
*/
