import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:http/http.dart' as http;
import '../models/chat_message.dart';
import '../models/pdf_file.dart';
import '../models/user_data.dart';
import 'local_storage_service.dart';
import 'connectivity_service.dart';
import 'sync_service.dart';

/// مدير الوضع غير المتصل
class OfflineManager {
  static final OfflineManager _instance = OfflineManager._internal();
  factory OfflineManager() => _instance;
  OfflineManager._internal();

  final LocalStorageService _localStorage = LocalStorageService();
  final ConnectivityService _connectivity = ConnectivityService();
  final SyncService _syncService = SyncService();

  bool _isInitialized = false;
  StreamController<OfflineStatus>? _statusController;

  /// تهيئة مدير الوضع غير المتصل
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _localStorage.initialize();
      await _syncService.initialize();

      _statusController = StreamController<OfflineStatus>.broadcast();

      // مراقبة حالة الاتصال
      _connectivity.connectionStream.listen((isConnected) {
        _updateStatus();
        if (isConnected) {
          _handleConnectionRestored();
        }
      });

      _isInitialized = true;
      debugPrint('✅ تم تهيئة مدير الوضع غير المتصل');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة مدير الوضع غير المتصل: $e');
      rethrow;
    }
  }

  /// الحصول على stream لحالة الوضع غير المتصل
  Stream<OfflineStatus> get statusStream {
    _statusController ??= StreamController<OfflineStatus>.broadcast();
    return _statusController!.stream;
  }

  /// الحصول على حالة الوضع غير المتصل الحالية
  OfflineStatus getCurrentStatus() {
    final isConnected = _connectivity.isConnected;
    final syncStatus = _syncService.getSyncStatus();
    final storageStats = _localStorage.getStorageStats();

    return OfflineStatus(
      isOnline: isConnected,
      hasOfflineData:
          storageStats['messages_count'] > 0 ||
          storageStats['pdf_files_count'] > 0,
      pendingSyncItems: syncStatus.pendingMessages,
      lastSyncTime: syncStatus.lastSyncTime,
      offlineCapabilities: _getOfflineCapabilities(),
    );
  }

  /// تحديث حالة الوضع غير المتصل
  void _updateStatus() {
    final status = getCurrentStatus();
    _statusController?.add(status);
  }

  /// التعامل مع استعادة الاتصال
  Future<void> _handleConnectionRestored() async {
    try {
      debugPrint('🌐 تم استعادة الاتصال - بدء المزامنة...');

      // مزامنة البيانات المعلقة
      final result = await _syncService.syncAll();

      if (result.success) {
        debugPrint('✅ تمت المزامنة بنجاح: ${result.itemsSynced} عنصر');
      } else {
        debugPrint('⚠️ مزامنة جزئية: ${result.message}');
      }

      _updateStatus();
    } catch (e) {
      debugPrint('❌ خطأ في مزامنة استعادة الاتصال: $e');
    }
  }

  /// الحصول على القدرات المتاحة في الوضع غير المتصل
  List<OfflineCapability> _getOfflineCapabilities() {
    return [
      OfflineCapability.viewMessages,
      OfflineCapability.writeMessages,
      OfflineCapability.viewPdfs,
      OfflineCapability.viewUserProfile,
      OfflineCapability.browseContent,
    ];
  }

  // ==================== إدارة الرسائل غير المتصلة ====================

  /// إرسال رسالة في الوضع غير المتصل
  Future<ChatMessage> sendOfflineMessage({
    required String content,
    required String senderId,
    required String senderName,
    required String roomId,
    String? replyToId,
    String? replyToContent,
    String? imageUrl,
    String? fileUrl,
    String? fileName,
    MessageType type = MessageType.text,
  }) async {
    try {
      final message = ChatMessage.createLocal(
        content: content,
        senderId: senderId,
        senderName: senderName,
        roomId: roomId,
        replyToId: replyToId,
        replyToContent: replyToContent,
        imageUrl: imageUrl,
        fileUrl: fileUrl,
        fileName: fileName,
        type: type,
      );

      // حفظ محلياً
      await _localStorage.saveMessage(message);

      // محاولة الإرسال إذا كان هناك اتصال
      if (_connectivity.isConnected) {
        await _syncService.syncMessage(message);
      }

      _updateStatus();
      debugPrint('📝 تم إنشاء رسالة غير متصلة: ${message.id}');

      return message;
    } catch (e) {
      debugPrint('❌ خطأ في إرسال رسالة غير متصلة: $e');
      rethrow;
    }
  }

  /// الحصول على رسائل غرفة (محلياً)
  List<ChatMessage> getOfflineMessages(String roomId, {int limit = 50}) {
    return _localStorage.getLatestMessagesForRoom(roomId, limit: limit);
  }

  /// البحث في الرسائل المحلية
  List<ChatMessage> searchOfflineMessages(String query, {String? roomId}) {
    try {
      List<ChatMessage> allMessages = [];

      if (roomId != null) {
        allMessages = _localStorage.getMessagesForRoom(roomId);
      } else {
        // البحث في جميع الرسائل إذا لم يتم تحديد غرفة
        final rooms = [
          'general',
          'first_year',
          'second_year',
          'third_year',
          'fourth_year',
        ];
        for (final room in rooms) {
          allMessages.addAll(_localStorage.getMessagesForRoom(room));
        }
      }

      return allMessages
          .where(
            (message) =>
                message.content.toLowerCase().contains(query.toLowerCase()) ||
                message.senderName.toLowerCase().contains(query.toLowerCase()),
          )
          .toList();
    } catch (e) {
      debugPrint('❌ خطأ في البحث في الرسائل: $e');
      return [];
    }
  }

  // ==================== إدارة ملفات PDF غير المتصلة ====================

  /// تحميل ملف PDF للوصول غير المتصل
  Future<bool> downloadPdfForOffline(PdfFile pdfFile) async {
    if (!_connectivity.isConnected) {
      debugPrint('⚠️ لا يمكن تحميل PDF بدون اتصال');
      return false;
    }

    if (pdfFile.onlineUrl == null) {
      debugPrint('⚠️ لا يوجد رابط للملف');
      return false;
    }

    try {
      debugPrint('📥 بدء تحميل PDF: ${pdfFile.name}');

      // إنشاء مجلد التحميل
      final directory = await getApplicationDocumentsDirectory();
      final pdfDir = Directory('${directory.path}/pdfs');
      if (!await pdfDir.exists()) {
        await pdfDir.create(recursive: true);
      }

      // تحميل الملف
      final response = await http.get(Uri.parse(pdfFile.onlineUrl!));
      if (response.statusCode == 200) {
        final fileName = '${pdfFile.id}.pdf';
        final filePath = '${pdfDir.path}/$fileName';
        final file = File(filePath);

        await file.writeAsBytes(response.bodyBytes);

        // تحديث معلومات الملف
        final updatedPdf = pdfFile.copyWith(
          localPath: filePath,
          isDownloaded: true,
          isAvailableOffline: true,
          fileSize: response.bodyBytes.length,
          downloadDate: DateTime.now(),
        );

        await _localStorage.savePdfFile(updatedPdf);

        debugPrint('✅ تم تحميل PDF بنجاح: ${pdfFile.name}');
        _updateStatus();
        return true;
      } else {
        debugPrint('❌ فشل تحميل PDF: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل PDF: $e');
      return false;
    }
  }

  /// الحصول على ملفات PDF المتاحة غير متصلة
  List<PdfFile> getOfflinePdfs({String? subject, String? category}) {
    final allPdfs = _localStorage.getAllPdfFiles();

    return allPdfs.where((pdf) {
      if (!pdf.isAvailableOffline) return false;
      if (subject != null && pdf.subject != subject) return false;
      if (category != null && pdf.category != category) return false;
      return true;
    }).toList();
  }

  /// فتح ملف PDF غير متصل
  Future<String?> openOfflinePdf(String pdfId) async {
    try {
      final allPdfs = _localStorage.getAllPdfFiles();
      final pdf = allPdfs.firstWhere((p) => p.id == pdfId);

      if (!pdf.isAvailableOffline || pdf.localPath == null) {
        debugPrint('⚠️ الملف غير متاح غير متصل');
        return null;
      }

      final file = File(pdf.localPath!);
      if (!await file.exists()) {
        debugPrint('⚠️ الملف المحلي غير موجود');
        // تحديث حالة الملف
        final updatedPdf = pdf.copyWith(
          isDownloaded: false,
          isAvailableOffline: false,
        );
        await _localStorage.savePdfFile(updatedPdf);
        return null;
      }

      // تحديث تاريخ آخر وصول
      final updatedPdf = pdf.markAsAccessed();
      await _localStorage.savePdfFile(updatedPdf);

      return pdf.localPath;
    } catch (e) {
      debugPrint('❌ خطأ في فتح PDF غير متصل: $e');
      return null;
    }
  }

  /// حذف ملف PDF من التخزين المحلي
  Future<bool> removeOfflinePdf(String pdfId) async {
    try {
      await _localStorage.deletePdfFile(pdfId);
      _updateStatus();
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في حذف PDF غير متصل: $e');
      return false;
    }
  }

  // ==================== إدارة البيانات العامة ====================

  /// الحصول على بيانات المستخدم المحلية
  UserData? getOfflineUserData() {
    return _localStorage.getUserData();
  }

  /// تحديث بيانات المستخدم محلياً
  Future<void> updateOfflineUserData(UserData userData) async {
    await _localStorage.saveUserData(userData);
    _updateStatus();
  }

  /// تنظيف البيانات القديمة
  Future<void> cleanupOfflineData() async {
    try {
      await _localStorage.cleanupOldData();
      _updateStatus();
      debugPrint('🧹 تم تنظيف البيانات القديمة');
    } catch (e) {
      debugPrint('❌ خطأ في تنظيف البيانات: $e');
    }
  }

  /// الحصول على إحصائيات التخزين
  Map<String, dynamic> getStorageStatistics() {
    final stats = _localStorage.getStorageStats();
    final offlinePdfs = getOfflinePdfs();

    // حساب حجم ملفات PDF
    int totalPdfSize = 0;
    for (final pdf in offlinePdfs) {
      totalPdfSize += pdf.fileSize;
    }

    return {
      ...stats,
      'offline_pdfs_count': offlinePdfs.length,
      'total_pdf_size': totalPdfSize,
      'formatted_pdf_size': _formatFileSize(totalPdfSize),
    };
  }

  /// تنسيق حجم الملف
  String _formatFileSize(int bytes) {
    if (bytes == 0) return '0 B';

    const units = ['B', 'KB', 'MB', 'GB'];
    double size = bytes.toDouble();
    int unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return '${size.toStringAsFixed(1)} ${units[unitIndex]}';
  }

  /// فرض المزامنة
  Future<SyncResult> forceSync() async {
    return await _syncService.forcSync();
  }

  /// تنظيف الموارد
  void dispose() {
    _statusController?.close();
    _syncService.dispose();
    debugPrint('🔒 تم إغلاق مدير الوضع غير المتصل');
  }
}

/// حالة الوضع غير المتصل
class OfflineStatus {
  final bool isOnline;
  final bool hasOfflineData;
  final int pendingSyncItems;
  final DateTime? lastSyncTime;
  final List<OfflineCapability> offlineCapabilities;

  OfflineStatus({
    required this.isOnline,
    required this.hasOfflineData,
    required this.pendingSyncItems,
    this.lastSyncTime,
    required this.offlineCapabilities,
  });

  /// التحقق من توفر قدرة معينة
  bool hasCapability(OfflineCapability capability) {
    return offlineCapabilities.contains(capability);
  }

  /// الحصول على وصف الحالة
  String get statusDescription {
    if (isOnline) {
      if (pendingSyncItems > 0) {
        return 'متصل - $pendingSyncItems عنصر في انتظار المزامنة';
      }
      return 'متصل ومتزامن';
    } else {
      if (hasOfflineData) {
        return 'غير متصل - البيانات المحفوظة متاحة';
      }
      return 'غير متصل - لا توجد بيانات محفوظة';
    }
  }

  @override
  String toString() {
    return 'OfflineStatus(online: $isOnline, hasData: $hasOfflineData, pending: $pendingSyncItems)';
  }
}

/// القدرات المتاحة في الوضع غير المتصل
enum OfflineCapability {
  viewMessages,
  writeMessages,
  viewPdfs,
  viewUserProfile,
  browseContent,
  searchContent,
}
