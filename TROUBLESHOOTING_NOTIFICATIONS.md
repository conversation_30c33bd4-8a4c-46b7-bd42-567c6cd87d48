# 🔧 دليل حل مشاكل نظام الإشعارات

## ❌ **المشكلة: "حدث خطأ أثناء تحميل الإشعارات"**

### 🔍 **الأسباب المحتملة:**

#### **1. مشكلة في قواعد Firestore**
- **الحل**: تطبيق قواعد Firestore الصحيحة
- **الملف**: `firestore_rules_notifications.txt`
- **الخطوات**:
  1. انتقل إلى Firebase Console
  2. Firestore Database > Rules
  3. انسخ القواعد من الملف
  4. اضغط "Publish"

#### **2. عدم وجود فهرس مركب (Composite Index)**
- **الحل**: إنشاء فهرس للاستعلام
- **الخطوات**:
  1. انتقل إلى Firebase Console
  2. Firestore Database > Indexes
  3. أضف فهرس مركب:
     - Collection: `notifications`
     - Fields: `userId` (Ascending), `createdAt` (Descending)

#### **3. عدم وجود مجموعة notifications**
- **الحل**: إنشاء المجموعة يدوياً
- **الخطوات**:
  1. انتقل إلى Firestore Database > Data
  2. أضف مجموعة جديدة: `notifications`
  3. أضف مستند تجريبي

#### **4. مشكلة في الاتصال بالإنترنت**
- **الحل**: التحقق من الاتصال
- **الاختبار**: فتح Firebase Console في المتصفح

---

## 🧪 **خطوات الاختبار:**

### **1. اختبار الإشعارات التجريبية:**
```dart
// في أي مكان في التطبيق
import 'lib/test_notifications.dart';
await runNotificationTests();
```

### **2. إنشاء إشعار تجريبي من الواجهة:**
1. افتح صفحة الإشعارات
2. اضغط "إنشاء إشعار تجريبي"
3. تحقق من ظهور الإشعار

### **3. اختبار رفع ملف:**
1. سجل دخول كأدمن (<EMAIL>)
2. ارفع ملف PDF جديد
3. تحقق من وصول الإشعار للطلاب

---

## 🔧 **حلول سريعة:**

### **الحل الأول: إعادة تعيين قواعد Firestore**
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

### **الحل الثاني: تبسيط الاستعلام**
تم تطبيقه بالفعل - إزالة `orderBy` لتجنب مشكلة الفهرس

### **الحل الثالث: إنشاء بيانات تجريبية**
استخدم زر "إنشاء إشعار تجريبي" في صفحة الإشعارات

---

## 📱 **اختبار على الجهاز:**

### **1. تشغيل التطبيق:**
```bash
flutter run
```

### **2. مراقبة الأخطاء:**
```bash
flutter logs
```

### **3. تحقق من Firebase:**
- انتقل إلى Firebase Console
- تحقق من Firestore Database
- راجع Authentication

---

## 🚨 **مشاكل شائعة وحلولها:**

### **خطأ: "Missing or insufficient permissions"**
- **السبب**: قواعد Firestore غير صحيحة
- **الحل**: تطبيق القواعد من `firestore_rules_notifications.txt`

### **خطأ: "The query requires an index"**
- **السبب**: عدم وجود فهرس مركب
- **الحل**: إنشاء فهرس في Firebase Console

### **خطأ: "Collection doesn't exist"**
- **السبب**: عدم وجود مجموعة notifications
- **الحل**: إنشاء المجموعة يدوياً أو استخدام الزر التجريبي

### **خطأ: "User not authenticated"**
- **السبب**: المستخدم غير مسجل دخول
- **الحل**: التأكد من تسجيل الدخول

---

## 🔄 **خطوات الإصلاح المرحلية:**

### **المرحلة 1: التحقق الأساسي**
1. ✅ تسجيل الدخول
2. ✅ الاتصال بالإنترنت
3. ✅ إعدادات Firebase

### **المرحلة 2: إعداد قاعدة البيانات**
1. ✅ قواعد Firestore
2. ✅ فهارس البيانات
3. ✅ مجموعة notifications

### **المرحلة 3: الاختبار**
1. ✅ إشعار تجريبي
2. ✅ رفع ملف حقيقي
3. ✅ استلام الإشعار

---

## 📞 **الدعم الإضافي:**

### **لوجات مفيدة:**
- تحقق من console التطبيق
- راجع Firebase Console > Usage
- تحقق من Network في Developer Tools

### **ملفات مهمة:**
- `lib/screens/notifications_screen.dart`
- `lib/services/notification_service.dart`
- `lib/models/notification_model.dart`
- `firestore_rules_notifications.txt`

### **أوامر مفيدة:**
```bash
flutter clean
flutter pub get
flutter analyze
flutter run --verbose
```

---

## ✅ **التحقق من نجاح الإصلاح:**

1. **فتح صفحة الإشعارات بدون أخطاء**
2. **ظهور رسالة "لا توجد إشعارات" بدلاً من خطأ**
3. **نجاح إنشاء إشعار تجريبي**
4. **وصول إشعارات حقيقية عند رفع ملفات**

إذا استمرت المشكلة، تحقق من Firebase Console وتأكد من إعداد المشروع بشكل صحيح.
