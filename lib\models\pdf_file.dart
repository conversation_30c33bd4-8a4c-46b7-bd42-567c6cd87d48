import 'package:hive/hive.dart';

part 'pdf_file.g.dart';

@HiveType(typeId: 1)
class PdfFile extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final String subject;

  @HiveField(3)
  final String category; // tests, exams, summaries, official_book

  @HiveField(4)
  final String? description;

  @HiveField(5)
  final String? onlineUrl;

  @HiveField(6)
  final String? localPath;

  @HiveField(7)
  final int fileSize;

  @HiveField(8)
  final DateTime uploadDate;

  @HiveField(9)
  final DateTime downloadDate;

  @HiveField(10)
  final bool isDownloaded;

  @HiveField(11)
  final bool isAvailableOffline;

  @HiveField(12)
  final String? thumbnailPath;

  @HiveField(13)
  final int pageCount;

  @HiveField(14)
  final String uploadedBy;

  @HiveField(15)
  final List<String> tags;

  @HiveField(16)
  final double? rating;

  @HiveField(17)
  final int downloadCount;

  @HiveField(18)
  final DateTime? lastAccessDate;

  @HiveField(19)
  final bool isFavorite;

  @HiveField(20)
  final String? checksum;

  PdfFile({
    required this.id,
    required this.name,
    required this.subject,
    required this.category,
    this.description,
    this.onlineUrl,
    this.localPath,
    this.fileSize = 0,
    required this.uploadDate,
    required this.downloadDate,
    this.isDownloaded = false,
    this.isAvailableOffline = false,
    this.thumbnailPath,
    this.pageCount = 0,
    this.uploadedBy = '',
    this.tags = const [],
    this.rating,
    this.downloadCount = 0,
    this.lastAccessDate,
    this.isFavorite = false,
    this.checksum,
  });

  /// إنشاء نسخة محدثة من ملف PDF
  PdfFile copyWith({
    String? id,
    String? name,
    String? subject,
    String? category,
    String? description,
    String? onlineUrl,
    String? localPath,
    int? fileSize,
    DateTime? uploadDate,
    DateTime? downloadDate,
    bool? isDownloaded,
    bool? isAvailableOffline,
    String? thumbnailPath,
    int? pageCount,
    String? uploadedBy,
    List<String>? tags,
    double? rating,
    int? downloadCount,
    DateTime? lastAccessDate,
    bool? isFavorite,
    String? checksum,
  }) {
    return PdfFile(
      id: id ?? this.id,
      name: name ?? this.name,
      subject: subject ?? this.subject,
      category: category ?? this.category,
      description: description ?? this.description,
      onlineUrl: onlineUrl ?? this.onlineUrl,
      localPath: localPath ?? this.localPath,
      fileSize: fileSize ?? this.fileSize,
      uploadDate: uploadDate ?? this.uploadDate,
      downloadDate: downloadDate ?? this.downloadDate,
      isDownloaded: isDownloaded ?? this.isDownloaded,
      isAvailableOffline: isAvailableOffline ?? this.isAvailableOffline,
      thumbnailPath: thumbnailPath ?? this.thumbnailPath,
      pageCount: pageCount ?? this.pageCount,
      uploadedBy: uploadedBy ?? this.uploadedBy,
      tags: tags ?? this.tags,
      rating: rating ?? this.rating,
      downloadCount: downloadCount ?? this.downloadCount,
      lastAccessDate: lastAccessDate ?? this.lastAccessDate,
      isFavorite: isFavorite ?? this.isFavorite,
      checksum: checksum ?? this.checksum,
    );
  }

  /// تحويل إلى Map للإرسال إلى Firebase
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'subject': subject,
      'category': category,
      'description': description,
      'onlineUrl': onlineUrl,
      'fileSize': fileSize,
      'uploadDate': uploadDate.millisecondsSinceEpoch,
      'pageCount': pageCount,
      'uploadedBy': uploadedBy,
      'tags': tags,
      'rating': rating,
      'downloadCount': downloadCount,
      'checksum': checksum,
    };
  }

  /// إنشاء من Map (من Firebase)
  factory PdfFile.fromMap(Map<String, dynamic> map) {
    return PdfFile(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      subject: map['subject'] ?? '',
      category: map['category'] ?? '',
      description: map['description'],
      onlineUrl: map['onlineUrl'],
      fileSize: map['fileSize'] ?? 0,
      uploadDate: DateTime.fromMillisecondsSinceEpoch(map['uploadDate'] ?? 0),
      downloadDate: DateTime.now(),
      pageCount: map['pageCount'] ?? 0,
      uploadedBy: map['uploadedBy'] ?? '',
      tags: List<String>.from(map['tags'] ?? []),
      rating: map['rating']?.toDouble(),
      downloadCount: map['downloadCount'] ?? 0,
      checksum: map['checksum'],
    );
  }

  /// الحصول على حجم الملف بصيغة قابلة للقراءة
  String get formattedFileSize {
    if (fileSize == 0) return 'غير معروف';
    
    const units = ['B', 'KB', 'MB', 'GB'];
    double size = fileSize.toDouble();
    int unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return '${size.toStringAsFixed(1)} ${units[unitIndex]}';
  }

  /// الحصول على اسم الفئة بالعربية
  String get categoryName {
    switch (category) {
      case 'tests':
        return 'أسئلة';
      case 'exams':
        return 'امتحانات';
      case 'summaries':
        return 'ملخصات';
      case 'official_book':
        return 'الكتاب الرسمي';
      default:
        return category;
    }
  }

  /// التحقق من إمكانية الوصول للملف
  bool get isAccessible => isDownloaded && localPath != null;

  /// التحقق من كون الملف حديث (أقل من أسبوع)
  bool get isRecent {
    return DateTime.now().difference(downloadDate).inDays < 7;
  }

  /// الحصول على نص مختصر للوصف
  String get shortDescription {
    if (description == null || description!.isEmpty) return 'لا يوجد وصف';
    if (description!.length <= 100) return description!;
    return '${description!.substring(0, 100)}...';
  }

  /// تحديث تاريخ آخر وصول
  PdfFile markAsAccessed() {
    return copyWith(lastAccessDate: DateTime.now());
  }

  /// زيادة عداد التحميل
  PdfFile incrementDownloadCount() {
    return copyWith(downloadCount: downloadCount + 1);
  }

  /// تبديل حالة المفضلة
  PdfFile toggleFavorite() {
    return copyWith(isFavorite: !isFavorite);
  }

  @override
  String toString() {
    return 'PdfFile(id: $id, name: $name, subject: $subject, category: $categoryName)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PdfFile && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// محول Hive لملفات PDF
class PdfFileAdapter extends TypeAdapter<PdfFile> {
  @override
  final int typeId = 1;

  @override
  PdfFile read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PdfFile(
      id: fields[0] as String,
      name: fields[1] as String,
      subject: fields[2] as String,
      category: fields[3] as String,
      description: fields[4] as String?,
      onlineUrl: fields[5] as String?,
      localPath: fields[6] as String?,
      fileSize: fields[7] as int,
      uploadDate: fields[8] as DateTime,
      downloadDate: fields[9] as DateTime,
      isDownloaded: fields[10] as bool,
      isAvailableOffline: fields[11] as bool,
      thumbnailPath: fields[12] as String?,
      pageCount: fields[13] as int,
      uploadedBy: fields[14] as String,
      tags: (fields[15] as List?)?.cast<String>() ?? [],
      rating: fields[16] as double?,
      downloadCount: fields[17] as int,
      lastAccessDate: fields[18] as DateTime?,
      isFavorite: fields[19] as bool,
      checksum: fields[20] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, PdfFile obj) {
    writer
      ..writeByte(21)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.subject)
      ..writeByte(3)
      ..write(obj.category)
      ..writeByte(4)
      ..write(obj.description)
      ..writeByte(5)
      ..write(obj.onlineUrl)
      ..writeByte(6)
      ..write(obj.localPath)
      ..writeByte(7)
      ..write(obj.fileSize)
      ..writeByte(8)
      ..write(obj.uploadDate)
      ..writeByte(9)
      ..write(obj.downloadDate)
      ..writeByte(10)
      ..write(obj.isDownloaded)
      ..writeByte(11)
      ..write(obj.isAvailableOffline)
      ..writeByte(12)
      ..write(obj.thumbnailPath)
      ..writeByte(13)
      ..write(obj.pageCount)
      ..writeByte(14)
      ..write(obj.uploadedBy)
      ..writeByte(15)
      ..write(obj.tags)
      ..writeByte(16)
      ..write(obj.rating)
      ..writeByte(17)
      ..write(obj.downloadCount)
      ..writeByte(18)
      ..write(obj.lastAccessDate)
      ..writeByte(19)
      ..write(obj.isFavorite)
      ..writeByte(20)
      ..write(obj.checksum);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PdfFileAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
