import 'package:hive/hive.dart';

part 'pdf_file.g.dart';

@HiveType(typeId: 2)
class PdfFile extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final String subject;

  @HiveField(3)
  final String category; // tests, exams, summaries, official_book

  @HiveField(4)
  final String? description;

  @HiveField(5)
  final String? onlineUrl;

  @HiveField(6)
  final String? localPath;

  @HiveField(7)
  final int fileSize;

  @HiveField(8)
  final DateTime uploadDate;

  @HiveField(9)
  final DateTime downloadDate;

  @HiveField(10)
  final bool isDownloaded;

  @HiveField(11)
  final bool isAvailableOffline;

  @HiveField(12)
  final String? thumbnailPath;

  @HiveField(13)
  final int pageCount;

  @HiveField(14)
  final String uploadedBy;

  @HiveField(15)
  final List<String> tags;

  @HiveField(16)
  final double? rating;

  @HiveField(17)
  final int downloadCount;

  @HiveField(18)
  final DateTime? lastAccessDate;

  @HiveField(19)
  final bool isFavorite;

  @HiveField(20)
  final String? checksum;

  PdfFile({
    required this.id,
    required this.name,
    required this.subject,
    required this.category,
    this.description,
    this.onlineUrl,
    this.localPath,
    this.fileSize = 0,
    required this.uploadDate,
    required this.downloadDate,
    this.isDownloaded = false,
    this.isAvailableOffline = false,
    this.thumbnailPath,
    this.pageCount = 0,
    this.uploadedBy = '',
    this.tags = const [],
    this.rating,
    this.downloadCount = 0,
    this.lastAccessDate,
    this.isFavorite = false,
    this.checksum,
  });

  /// إنشاء نسخة محدثة من ملف PDF
  PdfFile copyWith({
    String? id,
    String? name,
    String? subject,
    String? category,
    String? description,
    String? onlineUrl,
    String? localPath,
    int? fileSize,
    DateTime? uploadDate,
    DateTime? downloadDate,
    bool? isDownloaded,
    bool? isAvailableOffline,
    String? thumbnailPath,
    int? pageCount,
    String? uploadedBy,
    List<String>? tags,
    double? rating,
    int? downloadCount,
    DateTime? lastAccessDate,
    bool? isFavorite,
    String? checksum,
  }) {
    return PdfFile(
      id: id ?? this.id,
      name: name ?? this.name,
      subject: subject ?? this.subject,
      category: category ?? this.category,
      description: description ?? this.description,
      onlineUrl: onlineUrl ?? this.onlineUrl,
      localPath: localPath ?? this.localPath,
      fileSize: fileSize ?? this.fileSize,
      uploadDate: uploadDate ?? this.uploadDate,
      downloadDate: downloadDate ?? this.downloadDate,
      isDownloaded: isDownloaded ?? this.isDownloaded,
      isAvailableOffline: isAvailableOffline ?? this.isAvailableOffline,
      thumbnailPath: thumbnailPath ?? this.thumbnailPath,
      pageCount: pageCount ?? this.pageCount,
      uploadedBy: uploadedBy ?? this.uploadedBy,
      tags: tags ?? this.tags,
      rating: rating ?? this.rating,
      downloadCount: downloadCount ?? this.downloadCount,
      lastAccessDate: lastAccessDate ?? this.lastAccessDate,
      isFavorite: isFavorite ?? this.isFavorite,
      checksum: checksum ?? this.checksum,
    );
  }

  /// تحويل إلى Map للإرسال إلى Firebase
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'subject': subject,
      'category': category,
      'description': description,
      'onlineUrl': onlineUrl,
      'fileSize': fileSize,
      'uploadDate': uploadDate.millisecondsSinceEpoch,
      'pageCount': pageCount,
      'uploadedBy': uploadedBy,
      'tags': tags,
      'rating': rating,
      'downloadCount': downloadCount,
      'checksum': checksum,
    };
  }

  /// إنشاء من Map (من Firebase)
  factory PdfFile.fromMap(Map<String, dynamic> map) {
    return PdfFile(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      subject: map['subject'] ?? '',
      category: map['category'] ?? '',
      description: map['description'],
      onlineUrl: map['onlineUrl'],
      fileSize: map['fileSize'] ?? 0,
      uploadDate: DateTime.fromMillisecondsSinceEpoch(map['uploadDate'] ?? 0),
      downloadDate: DateTime.now(),
      pageCount: map['pageCount'] ?? 0,
      uploadedBy: map['uploadedBy'] ?? '',
      tags: List<String>.from(map['tags'] ?? []),
      rating: map['rating']?.toDouble(),
      downloadCount: map['downloadCount'] ?? 0,
      checksum: map['checksum'],
    );
  }

  /// الحصول على حجم الملف بصيغة قابلة للقراءة
  String get formattedFileSize {
    if (fileSize == 0) return 'غير معروف';

    const units = ['B', 'KB', 'MB', 'GB'];
    double size = fileSize.toDouble();
    int unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return '${size.toStringAsFixed(1)} ${units[unitIndex]}';
  }

  /// الحصول على اسم الفئة بالعربية
  String get categoryName {
    switch (category) {
      case 'tests':
        return 'أسئلة';
      case 'exams':
        return 'امتحانات';
      case 'summaries':
        return 'ملخصات';
      case 'official_book':
        return 'الكتاب الرسمي';
      default:
        return category;
    }
  }

  /// التحقق من إمكانية الوصول للملف
  bool get isAccessible => isDownloaded && localPath != null;

  /// التحقق من كون الملف حديث (أقل من أسبوع)
  bool get isRecent {
    return DateTime.now().difference(downloadDate).inDays < 7;
  }

  /// الحصول على نص مختصر للوصف
  String get shortDescription {
    if (description == null || description!.isEmpty) return 'لا يوجد وصف';
    if (description!.length <= 100) return description!;
    return '${description!.substring(0, 100)}...';
  }

  /// تحديث تاريخ آخر وصول
  PdfFile markAsAccessed() {
    return copyWith(lastAccessDate: DateTime.now());
  }

  /// زيادة عداد التحميل
  PdfFile incrementDownloadCount() {
    return copyWith(downloadCount: downloadCount + 1);
  }

  /// تبديل حالة المفضلة
  PdfFile toggleFavorite() {
    return copyWith(isFavorite: !isFavorite);
  }

  @override
  String toString() {
    return 'PdfFile(id: $id, name: $name, subject: $subject, category: $categoryName)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PdfFile && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
