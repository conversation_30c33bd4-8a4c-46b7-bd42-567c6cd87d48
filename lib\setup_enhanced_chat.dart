import 'package:flutter/foundation.dart';
import 'utils/chat_system_integration.dart';
import 'test_enhanced_chat_system.dart';

/// إعداد سريع لنظام الدردشة المحسن
class EnhancedChatSetup {
  
  /// تهيئة النظام المحسن
  static Future<void> initialize() async {
    try {
      if (kDebugMode) {
        print('🚀 بدء تهيئة نظام الدردشة المحسن...');
      }

      // تهيئة النظام المدمج
      await ChatSystemIntegration().initialize();

      if (kDebugMode) {
        print('✅ تم تهيئة نظام الدردشة المحسن بنجاح');
        
        // تشغيل اختبار سريع في وضع التطوير
        await EnhancedChatSystemTester.quickTest();
        
        // طباعة تقرير النظام
        ChatSystemIntegration().printSystemReport();
      }

    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تهيئة نظام الدردشة المحسن: $e');
        print('🔄 سيتم استخدام النظام القديم كبديل');
      }
    }
  }

  /// تشغيل اختبارات شاملة
  static Future<void> runTests() async {
    if (kDebugMode) {
      await EnhancedChatSystemTester.runAllTests();
    }
  }

  /// التحقق من حالة النظام
  static bool isSystemReady() {
    return ChatSystemIntegration().isEnhancedModeEnabled;
  }

  /// الحصول على معلومات النظام
  static Map<String, dynamic> getSystemInfo() {
    return ChatSystemIntegration().getSystemStats();
  }
}

/// دالة مساعدة للتهيئة السريعة
Future<void> setupEnhancedChat() async {
  await EnhancedChatSetup.initialize();
}

/// دالة مساعدة للاختبار
Future<void> testEnhancedChat() async {
  await EnhancedChatSetup.runTests();
}
