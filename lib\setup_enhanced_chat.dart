import 'package:flutter/foundation.dart';
import 'utils/chat_system_integration.dart';

/// إعداد سريع لنظام الدردشة المحسن
class EnhancedChatSetup {
  /// تهيئة النظام المحسن
  static Future<void> initialize() async {
    try {
      if (kDebugMode) {
        debugPrint('🚀 بدء تهيئة نظام الدردشة المحسن...');
      }

      // تهيئة النظام المدمج
      await ChatSystemIntegration().initialize();

      if (kDebugMode) {
        debugPrint('✅ تم تهيئة نظام الدردشة المحسن بنجاح');

        // طباعة تقرير النظام
        ChatSystemIntegration().printSystemReport();
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في تهيئة نظام الدردشة المحسن: $e');
        debugPrint('🔄 سيتم استخدام النظام القديم كبديل');
      }
    }
  }

  /// تشغيل اختبار النظام
  static Future<void> runTests() async {
    if (kDebugMode) {
      await ChatSystemIntegration().runSystemTest();
    }
  }

  /// التحقق من حالة النظام
  static bool isSystemReady() {
    return ChatSystemIntegration().isEnhancedModeEnabled;
  }

  /// الحصول على معلومات النظام
  static Map<String, dynamic> getSystemInfo() {
    return ChatSystemIntegration().getSystemStats();
  }
}

/// دالة مساعدة للتهيئة السريعة
Future<void> setupEnhancedChat() async {
  await EnhancedChatSetup.initialize();
}

/// دالة مساعدة للاختبار
Future<void> testEnhancedChat() async {
  await EnhancedChatSetup.runTests();
}
